import { NextRequest } from 'next/server';
import { UserManager } from '@/lib/auth';
import { 
  successResponse, 
  errorResponse, 
  validateRequest, 
  validators,
  logAuditEvent,
  withAdminAccess
} from '@/lib/api-utils';

// POST /api/users/[id]/ban - Ban user (admin only)
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const userId = parseInt(params.id);
      if (isNaN(userId)) {
        return errorResponse('用户ID无效', 400);
      }

      // Don't allow banning yourself
      if (userId === user.id) {
        return errorResponse('不能封禁自己', 400);
      }

      const body = await request.json();
      
      // Validate request
      const { isValid, errors, data } = validateRequest<{
        reason: string;
        expiresAt?: string;
      }>(body, {
        reason: validators.required,
        expiresAt: (value) => {
          if (!value) return null;
          const date = new Date(value);
          if (isNaN(date.getTime())) {
            return '日期格式无效';
          }
          if (date <= new Date()) {
            return '过期时间必须是未来时间';
          }
          return null;
        },
      });

      if (!isValid) {
        return errorResponse('验证失败', 400, 'VALIDATION_ERROR');
      }

      // Check if target user exists
      const targetUser = await UserManager.getUserById(userId);
      if (!targetUser) {
        return errorResponse('用户不存在', 404);
      }

      // Don't allow banning admins (unless you're a super admin)
      if (targetUser.role === 'admin' && user.role !== 'admin') {
        return errorResponse('不能封禁管理员用户', 403);
      }

      // Ban the user
      const expiresAt = data.expiresAt ? new Date(data.expiresAt) : undefined;
      await UserManager.banUser(userId, data.reason, user.id, expiresAt);

      await logAuditEvent({
        userId: user.id,
        action: 'USER_BANNED',
        resource: 'users',
        resourceId: userId.toString(),
        details: {
          targetUserId: userId,
          reason: data.reason,
          expiresAt: expiresAt?.toISOString(),
        },
      });

      return successResponse(null, '用户封禁成功');
    } catch (error) {
      console.error('Ban user error:', error);
      return errorResponse('封禁用户失败', 500);
    }
  });
}
