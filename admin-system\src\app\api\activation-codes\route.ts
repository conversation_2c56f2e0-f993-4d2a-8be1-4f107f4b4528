import { NextRequest } from 'next/server';
import { ActivationCodeManager } from '@/lib/activation-codes';
import {
  withAdminAccess,
  successResponse,
  errorResponse,
  validateRequest,
  validators,
  getPaginationParams,
  logAuditEvent
} from '@/lib/api-utils';

// GET /api/activation-codes - 获取激活码列表 (仅管理员)
export async function GET(request: NextRequest) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const url = new URL(request.url);
      const searchParams = url.searchParams;

      // 解析分页参数
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '20');

      // 解析筛选参数
      const search = searchParams.get('search') || undefined;
      const vip_level = searchParams.get('vip_level') || undefined;
      const status = searchParams.get('status') || undefined;
      const batch_id = searchParams.get('batch_id') || undefined;
      const created_by = searchParams.get('created_by') ? parseInt(searchParams.get('created_by')!) : undefined;
      const sort_by = searchParams.get('sort_by') || 'created_at';
      const sort_order = (searchParams.get('sort_order') || 'desc') as 'asc' | 'desc';

      const { codes, total } = await ActivationCodeManager.getActivationCodesWithFilters({
        page,
        limit,
        search,
        vip_level,
        status,
        batch_id,
        created_by,
        sort_by,
        sort_order,
      });

      const totalPages = Math.ceil(total / limit);

      await logAuditEvent({
        userId: user.id,
        action: 'ACTIVATION_CODES_VIEWED',
        resource: 'activation_codes',
        details: {
          page,
          limit,
          total,
          filters: { search, vip_level, status, batch_id, created_by }
        },
      });

      return successResponse(
        {
          codes,
          pagination: {
            page,
            limit,
            total,
            totalPages,
          }
        },
        '激活码列表获取成功'
      );
    } catch (error) {
      console.error('Get activation codes error:', error);
      return errorResponse('获取激活码列表失败', 500);
    }
  });
}

// POST /api/activation-codes - 创建激活码 (仅管理员)
export async function POST(request: NextRequest) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const body = await request.json();

      // 验证请求数据
      const { isValid, errors, data } = validateRequest<{
        vip_level: 'v1' | 'v2' | 'v3' | 'v4';
        vip_duration_days?: number;
        expires_at?: string;
        description?: string;
        count?: number; // 批量生成数量
      }>(body, {
        vip_level: (value) => {
          const validLevels = ['v1', 'v2', 'v3', 'v4'];
          if (!validLevels.includes(value)) {
            return 'Invalid VIP level';
          }
          return null;
        },
        vip_duration_days: (value) => {
          if (value !== undefined && (typeof value !== 'number' || value <= 0)) {
            return 'VIP duration must be a positive number';
          }
          return null;
        },
        expires_at: (value) => {
          if (value && isNaN(new Date(value).getTime())) {
            return 'Invalid expiration date format';
          }
          return null;
        },
        count: (value) => {
          if (value !== undefined && (typeof value !== 'number' || value <= 0 || value > 100)) {
            return 'Count must be between 1 and 100';
          }
          return null;
        },
      });

      if (!isValid) {
        return errorResponse('Validation failed', 400, 'VALIDATION_ERROR', errors);
      }

      const {
        vip_level,
        vip_duration_days,
        expires_at,
        description,
        count = 1
      } = data;

      const expiresAtDate = expires_at ? new Date(expires_at) : undefined;

      let codes;
      if (count === 1) {
        // 创建单个激活码
        const code = await ActivationCodeManager.createActivationCode({
          vip_level,
          vip_duration_days,
          expires_at: expiresAtDate,
          description,
          created_by: user.id
        });
        codes = [code];
      } else {
        // 批量创建激活码
        codes = await ActivationCodeManager.createBatchActivationCodes({
          vip_level,
          vip_duration_days,
          expires_at: expiresAtDate,
          description,
          created_by: user.id
        }, count);
      }

      await logAuditEvent({
        userId: user.id,
        action: 'ACTIVATION_CODES_CREATED',
        resource: 'activation_codes',
        details: {
          count: codes.length,
          vip_level,
          vip_duration_days,
          expires_at: expiresAtDate?.toISOString(),
          batch_id: codes[0]?.batch_id
        },
      });

      return successResponse(
        codes,
        `${codes.length} activation code(s) created successfully`
      );
    } catch (error) {
      console.error('Create activation codes error:', error);
      return errorResponse('Failed to create activation codes', 500);
    }
  });
}
