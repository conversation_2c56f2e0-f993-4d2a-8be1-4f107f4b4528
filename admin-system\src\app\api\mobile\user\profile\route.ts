/**
 * 移动端用户资料API
 * 专门为UniApp等移动客户端提供的用户资料接口
 */

import { NextRequest } from 'next/server';
import { UserManager } from '@/lib/auth';
import { 
  withUserAccess,
  successResponse, 
  errorResponse,
  validateRequest,
  validators,
  logAuditEvent
} from '@/lib/api-utils';
import { ClientDetector } from '@/lib/client-detection';

// GET /api/mobile/user/profile - 获取当前用户资料
export async function GET(request: NextRequest) {
  return withUserAccess()(request, async (req, user, session, clientInfo) => {
    try {
      // 确保是移动端客户端
      if (!ClientDetector.isMobileClient(clientInfo.type)) {
        return errorResponse('此接口仅供移动端客户端使用', 403, 'INVALID_CLIENT');
      }

      // 获取完整的用户信息
      const fullUser = await UserManager.getUserById(user.id);
      
      if (!fullUser) {
        return errorResponse('用户不存在', 404);
      }

      // 记录访问
      await logAuditEvent({
        userId: user.id,
        action: 'MOBILE_PROFILE_VIEWED',
        resource: 'user-profile',
        details: {
          clientType: clientInfo.type,
        },
      });

      // 返回移动端适用的用户信息
      return successResponse({
        id: fullUser.id,
        username: fullUser.username,
        full_name: fullUser.full_name,
        avatar_url: fullUser.avatar_url,
        created_at: fullUser.created_at,
        last_login: fullUser.last_login,
        // 移动端不返回敏感信息如role等
        preferences: {
          // 移动端用户偏好设置
          notifications_enabled: true,
          theme: 'auto',
          language: 'zh-CN',
        }
      }, '获取用户资料成功');

    } catch (error) {
      console.error('Get mobile profile error:', error);
      return errorResponse('获取用户资料失败', 500);
    }
  });
}

// PUT /api/mobile/user/profile - 更新用户资料
export async function PUT(request: NextRequest) {
  return withUserAccess()(request, async (req, user, session, clientInfo) => {
    try {
      // 确保是移动端客户端
      if (!ClientDetector.isMobileClient(clientInfo.type)) {
        return errorResponse('此接口仅供移动端客户端使用', 403, 'INVALID_CLIENT');
      }

      const body = await request.json();

      // 验证请求数据
      const { isValid, errors, data } = validateRequest<{
        full_name?: string;
        avatar_url?: string;
        preferences?: {
          notifications_enabled?: boolean;
          theme?: string;
          language?: string;
        };
      }>(body, {
        full_name: (value) => {
          if (value && typeof value !== 'string') return '姓名必须是字符串';
          if (value && value.length > 50) return '姓名不能超过50个字符';
          return null;
        },
        avatar_url: (value) => {
          if (value && typeof value !== 'string') return '头像URL必须是字符串';
          if (value && value.length > 500) return '头像URL过长';
          return null;
        },
      });

      if (!isValid) {
        return errorResponse('验证失败', 400, 'VALIDATION_ERROR');
      }

      // 准备更新数据（只允许更新特定字段）
      const updateData: any = {};
      
      if (data.full_name !== undefined) {
        updateData.full_name = data.full_name;
      }
      
      if (data.avatar_url !== undefined) {
        updateData.avatar_url = data.avatar_url;
      }

      // 更新用户信息
      const updatedUser = await UserManager.updateUser(user.id, updateData);

      if (!updatedUser) {
        return errorResponse('更新用户资料失败', 500);
      }

      // 记录更新操作
      await logAuditEvent({
        userId: user.id,
        action: 'MOBILE_PROFILE_UPDATED',
        resource: 'user-profile',
        details: {
          updatedFields: Object.keys(updateData),
          clientType: clientInfo.type,
        },
      });

      // 返回更新后的用户信息
      return successResponse({
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        full_name: updatedUser.full_name,
        avatar_url: updatedUser.avatar_url,
        updated_at: updatedUser.updated_at,
      }, '用户资料更新成功');

    } catch (error) {
      console.error('Update mobile profile error:', error);
      return errorResponse('更新用户资料失败', 500);
    }
  });
}

// POST /api/mobile/user/profile/avatar - 上传头像
export async function POST(request: NextRequest) {
  return withUserAccess()(request, async (req, user, session, clientInfo) => {
    try {
      // 确保是移动端客户端
      if (!ClientDetector.isMobileClient(clientInfo.type)) {
        return errorResponse('此接口仅供移动端客户端使用', 403, 'INVALID_CLIENT');
      }

      // 这里应该处理文件上传逻辑
      // 由于这是一个示例，我们返回一个模拟的响应
      
      const body = await request.json();
      const { base64Image, fileName } = body;

      if (!base64Image) {
        return errorResponse('缺少图片数据', 400);
      }

      // 模拟头像上传处理
      // 实际实现中应该：
      // 1. 验证图片格式和大小
      // 2. 保存到文件存储服务
      // 3. 生成访问URL
      // 4. 更新用户头像URL

      const avatarUrl = `https://example.com/avatars/${user.id}_${Date.now()}.jpg`;

      // 更新用户头像
      const updatedUser = await UserManager.updateUser(user.id, {
        avatar_url: avatarUrl
      });

      // 记录头像更新
      await logAuditEvent({
        userId: user.id,
        action: 'MOBILE_AVATAR_UPDATED',
        resource: 'user-avatar',
        details: {
          fileName,
          avatarUrl,
          clientType: clientInfo.type,
        },
      });

      return successResponse({
        avatar_url: avatarUrl,
        updated_at: new Date().toISOString(),
      }, '头像上传成功');

    } catch (error) {
      console.error('Upload mobile avatar error:', error);
      return errorResponse('头像上传失败', 500);
    }
  });
}
