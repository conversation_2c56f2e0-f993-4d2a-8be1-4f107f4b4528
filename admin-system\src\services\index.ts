/**
 * 服务模块统一导出
 * 提供所有API服务的统一入口
 */

// 导出服务类
export { AuthService } from './auth.service';
export { UserService } from './user.service';

// 导出类型定义
export type {
  LoginRequest,
  RegisterRequest,
  User,
  AuthResponse,
  ChangePasswordRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
} from './auth.service';

export type {
  CreateUserRequest,
  UpdateUserRequest,
  UserListParams,
  UserListResponse,
  UserStats,
} from './user.service';

// 导入所有服务
import { AuthService } from './auth.service';
import { UserService } from './user.service';

// 创建统一的API服务对象
export const API = {
  auth: AuthService,
  user: UserService,
} as const;

// 默认导出
export default API;

// 服务工厂函数，用于创建带有通用配置的服务实例
export class ServiceFactory {
  private static baseConfig = {
    timeout: 10000,
    retryCount: 3,
    showError: true,
  };

  /**
   * 设置全局服务配置
   */
  static setGlobalConfig(config: Partial<typeof ServiceFactory.baseConfig>): void {
    Object.assign(this.baseConfig, config);
  }

  /**
   * 获取全局服务配置
   */
  static getGlobalConfig(): typeof ServiceFactory.baseConfig {
    return { ...this.baseConfig };
  }

  /**
   * 创建带有自定义配置的服务实例
   */
  static createService<T extends keyof typeof API>(
    serviceName: T,
    config?: Partial<typeof ServiceFactory.baseConfig>
  ): typeof API[T] {
    // 这里可以根据需要对服务进行配置
    // 目前返回原始服务，后续可以扩展为代理对象来应用配置
    if (config) {
      // 配置逻辑可以在这里实现
    }
    return API[serviceName];
  }
}

// 便捷的服务访问器
export const Services = {
  /**
   * 认证服务
   */
  get auth() {
    return AuthService;
  },

  /**
   * 用户管理服务
   */
  get user() {
    return UserService;
  },

  /**
   * 获取所有服务
   */
  getAll() {
    return API;
  },

  /**
   * 检查服务是否可用
   */
  async healthCheck(): Promise<boolean> {
    try {
      // 这里可以添加健康检查逻辑
      // 例如调用一个健康检查端点
      return true;
    } catch (error) {
      console.error('Service health check failed:', error);
      return false;
    }
  },
};

// 服务状态管理
export class ServiceStatus {
  private static status: Record<string, 'online' | 'offline' | 'error'> = {};

  /**
   * 设置服务状态
   */
  static setStatus(service: string, status: 'online' | 'offline' | 'error'): void {
    this.status[service] = status;
  }

  /**
   * 获取服务状态
   */
  static getStatus(service: string): 'online' | 'offline' | 'error' | 'unknown' {
    return this.status[service] || 'unknown';
  }

  /**
   * 获取所有服务状态
   */
  static getAllStatus(): Record<string, 'online' | 'offline' | 'error' | 'unknown'> {
    return { ...this.status };
  }

  /**
   * 重置所有服务状态
   */
  static reset(): void {
    this.status = {};
  }
}

// 服务错误处理器
export class ServiceErrorHandler {
  private static handlers: Record<string, (error: Error) => void> = {};

  /**
   * 注册错误处理器
   */
  static register(errorType: string, handler: (error: Error) => void): void {
    this.handlers[errorType] = handler;
  }

  /**
   * 处理错误
   */
  static handle(errorType: string, error: Error): void {
    const handler = this.handlers[errorType];
    if (handler) {
      handler(error);
    } else {
      console.error(`Unhandled service error (${errorType}):`, error);
    }
  }

  /**
   * 移除错误处理器
   */
  static unregister(errorType: string): void {
    delete this.handlers[errorType];
  }

  /**
   * 清除所有错误处理器
   */
  static clear(): void {
    this.handlers = {};
  }
}

// 服务缓存管理
export class ServiceCache {
  private static cache = new Map<string, { data: unknown; timestamp: number; ttl: number }>();

  /**
   * 设置缓存
   */
  static set(key: string, data: unknown, ttl: number = 300000): void { // 默认5分钟
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  /**
   * 获取缓存
   */
  static get<T = unknown>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    const now = Date.now();
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  /**
   * 删除缓存
   */
  static delete(key: string): boolean {
    return this.cache.delete(key);
  }

  /**
   * 清除所有缓存
   */
  static clear(): void {
    this.cache.clear();
  }

  /**
   * 清除过期缓存
   */
  static clearExpired(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// 定期清理过期缓存
if (typeof window !== 'undefined') {
  setInterval(() => {
    ServiceCache.clearExpired();
  }, 60000); // 每分钟清理一次
}
