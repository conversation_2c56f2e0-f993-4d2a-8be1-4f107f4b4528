import { NextRequest } from 'next/server';
import { UserManager } from '@/lib/auth';
import { 
  withAuth,
  withRole,
  successResponse, 
  errorResponse,
  logAuditEvent
} from '@/lib/api-utils';

// GET /api/users/[id] - Get user by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withAuth(request, async (req, user, session) => {
    try {
      const resolvedParams = await params;
      const userId = parseInt(resolvedParams.id);
      
      if (isNaN(userId)) {
        return errorResponse('Invalid user ID', 400);
      }

      // Users can only view their own profile unless they're admin
      if (user.role !== 'admin' && user.id !== userId) {
        return errorResponse('Insufficient permissions', 403);
      }

      const targetUser = await UserManager.getUserById(userId);
      
      if (!targetUser) {
        return errorResponse('User not found', 404);
      }

      await logAuditEvent({
        userId: user.id,
        action: 'USER_VIEWED',
        resource: 'users',
        resourceId: userId.toString(),
      });

      return successResponse({
        id: targetUser.id,
        username: targetUser.username,
        email: targetUser.email,
        full_name: targetUser.full_name,
        role: targetUser.role,
        avatar_url: targetUser.avatar_url,
        is_active: targetUser.is_active,
        last_login: targetUser.last_login,
        created_at: targetUser.created_at,
        updated_at: targetUser.updated_at,
      });
    } catch (error) {
      console.error('Get user error:', error);
      return errorResponse('Failed to retrieve user', 500);
    }
  });
}

// PUT /api/users/[id] - Update user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withAuth(request, async (req, user, session) => {
    try {
      const resolvedParams = await params;
      const userId = parseInt(resolvedParams.id);
      
      if (isNaN(userId)) {
        return errorResponse('Invalid user ID', 400);
      }

      // Users can only update their own profile unless they're admin
      if (user.role !== 'admin' && user.id !== userId) {
        return errorResponse('Insufficient permissions', 403);
      }

      const targetUser = await UserManager.getUserById(userId);
      
      if (!targetUser) {
        return errorResponse('User not found', 404);
      }

      const body = await request.json();
      const { full_name, avatar_url, role, is_active } = body;

      // Prepare updates
      const updates: any = {};
      
      if (full_name !== undefined) {
        updates.full_name = full_name;
      }
      
      if (avatar_url !== undefined) {
        updates.avatar_url = avatar_url;
      }

      // Only admins can update role and active status
      if (user.role === 'admin') {
        if (role !== undefined) {
          if (!['admin', 'user', 'moderator'].includes(role)) {
            return errorResponse('Invalid role', 400);
          }
          updates.role = role;
        }
        
        if (is_active !== undefined) {
          updates.is_active = is_active;
        }
      }

      // Update user
      const updatedUser = await UserManager.updateUser(userId, updates);

      await logAuditEvent({
        userId: user.id,
        action: 'USER_UPDATED',
        resource: 'users',
        resourceId: userId.toString(),
        details: {
          updates,
          targetUserId: userId,
        },
      });

      return successResponse({
        id: updatedUser!.id,
        username: updatedUser!.username,
        email: updatedUser!.email,
        full_name: updatedUser!.full_name,
        role: updatedUser!.role,
        avatar_url: updatedUser!.avatar_url,
        is_active: updatedUser!.is_active,
        last_login: updatedUser!.last_login,
        created_at: updatedUser!.created_at,
        updated_at: updatedUser!.updated_at,
      }, 'User updated successfully');
    } catch (error) {
      console.error('Update user error:', error);
      return errorResponse('Failed to update user', 500);
    }
  });
}

// DELETE /api/users/[id] - Delete user (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withRole(['admin'])(request, async (req, user, session) => {
    try {
      const resolvedParams = await params;
      const userId = parseInt(resolvedParams.id);
      
      if (isNaN(userId)) {
        return errorResponse('Invalid user ID', 400);
      }

      // Prevent self-deletion
      if (user.id === userId) {
        return errorResponse('Cannot delete your own account', 400);
      }

      const targetUser = await UserManager.getUserById(userId);
      
      if (!targetUser) {
        return errorResponse('User not found', 404);
      }

      await UserManager.deleteUser(userId);

      await logAuditEvent({
        userId: user.id,
        action: 'USER_DELETED',
        resource: 'users',
        resourceId: userId.toString(),
        details: {
          deletedUser: {
            id: targetUser.id,
            username: targetUser.username,
            email: targetUser.email,
            role: targetUser.role,
          },
        },
      });

      return successResponse(null, 'User deleted successfully');
    } catch (error) {
      console.error('Delete user error:', error);
      return errorResponse('Failed to delete user', 500);
    }
  });
}
