import { NextRequest } from 'next/server';
import { UserManager } from '@/lib/auth';
import { 
  successResponse, 
  errorResponse, 
  logAuditEvent,
  withAdminAccess
} from '@/lib/api-utils';

// POST /api/users/[id]/unban - Unban user (admin only)
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const userId = parseInt(params.id);
      if (isNaN(userId)) {
        return errorResponse('Invalid user ID', 400);
      }

      // Check if target user exists
      const targetUser = await UserManager.getUserById(userId);
      if (!targetUser) {
        return errorResponse('User not found', 404);
      }

      // Check if user is actually banned
      if (!targetUser.is_banned) {
        return errorResponse('User is not banned', 400);
      }

      // Unban the user
      await UserManager.unbanUser(userId);

      await logAuditEvent({
        userId: user.id,
        action: 'USER_UNBANNED',
        resource: 'users',
        resourceId: userId.toString(),
        details: {
          targetUserId: userId,
        },
      });

      return successResponse(null, 'User unbanned successfully');
    } catch (error) {
      console.error('Unban user error:', error);
      return errorResponse('Failed to unban user', 500);
    }
  });
}
