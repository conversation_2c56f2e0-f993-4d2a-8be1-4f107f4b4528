/**
 * 激活码管理模块
 * 提供激活码的生成、验证、使用等核心功能
 */

import { db } from './database';
import crypto from 'crypto';

// 激活码相关类型定义
export interface ActivationCode {
  id: number;
  code: string;
  vip_level: 'v1' | 'v2' | 'v3' | 'v4';
  vip_duration_days: number;
  status: 'active' | 'used' | 'expired' | 'disabled';
  used_by?: number;
  used_at?: Date;
  created_by: number;
  created_at: Date;
  expires_at?: Date;
  description?: string;
  batch_id?: string;
  // 关联的用户信息
  user?: {
    id: number;
    username: string;
    email: string;
    full_name?: string;
  };
  // 创建者信息
  creator?: {
    id: number;
    username: string;
    email: string;
  };
}

export interface ActivationCodeLog {
  id: number;
  activation_code_id: number;
  user_id?: number;
  admin_id?: number;
  action: 'created' | 'used' | 'expired' | 'disabled' | 'enabled';
  old_status?: string;
  new_status?: string;
  details?: any;
  ip_address?: string;
  user_agent?: string;
  created_at: Date;
}

export interface CreateActivationCodeParams {
  vip_level: 'v1' | 'v2' | 'v3' | 'v4';
  vip_duration_days?: number;
  expires_at?: Date;
  description?: string;
  batch_id?: string;
  created_by: number;
}

export interface ActivationCodeFilters {
  page?: number;
  limit?: number;
  search?: string;
  vip_level?: string;
  status?: string;
  batch_id?: string;
  created_by?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface ActivationCodeStats {
  total: number;
  active: number;
  used: number;
  expired: number;
  disabled: number;
  by_level: {
    v1: number;
    v2: number;
    v3: number;
    v4: number;
  };
}

// VIP等级配置
export const VIP_LEVEL_CONFIG = {
  v1: {
    label: 'VIP V1',
    description: '基础VIP会员',
    default_duration: 30,
    color: '#10B981', // green
  },
  v2: {
    label: 'VIP V2', 
    description: '进阶VIP会员',
    default_duration: 60,
    color: '#3B82F6', // blue
  },
  v3: {
    label: 'VIP V3',
    description: '高级VIP会员', 
    default_duration: 90,
    color: '#8B5CF6', // purple
  },
  v4: {
    label: 'VIP V4',
    description: '至尊VIP会员',
    default_duration: 180,
    color: '#F59E0B', // amber
  },
} as const;

// 激活码管理器
export class ActivationCodeManager {
  /**
   * 生成激活码字符串
   */
  static generateCode(): string {
    // 生成16位随机字符串，包含数字和大写字母
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 16; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    // 格式化为 XXXX-XXXX-XXXX-XXXX
    return result.replace(/(.{4})/g, '$1-').slice(0, -1);
  }

  /**
   * 创建单个激活码
   */
  static async createActivationCode(params: CreateActivationCodeParams): Promise<ActivationCode> {
    const {
      vip_level,
      vip_duration_days = VIP_LEVEL_CONFIG[vip_level].default_duration,
      expires_at,
      description,
      batch_id,
      created_by
    } = params;

    // 生成唯一激活码
    let code: string;
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 10;

    while (!isUnique && attempts < maxAttempts) {
      code = this.generateCode();
      const existing = await db.queryOne<{ id: number }>(
        'SELECT id FROM activation_codes WHERE code = ?',
        [code]
      );
      isUnique = !existing;
      attempts++;
    }

    if (!isUnique) {
      throw new Error('Failed to generate unique activation code');
    }

    // 插入数据库
    const result = await db.query(
      `INSERT INTO activation_codes
       (code, vip_level, vip_duration_days, expires_at, description, batch_id, created_by)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        code!,
        vip_level,
        vip_duration_days,
        expires_at || null,
        description || null,
        batch_id || null,
        created_by
      ]
    );

    const insertId = (result as any).insertId;

    // 记录日志
    await this.logAction({
      activation_code_id: insertId,
      admin_id: created_by,
      action: 'created',
      new_status: 'active',
      details: { vip_level, vip_duration_days, expires_at, description, batch_id }
    });

    // 返回创建的激活码
    return await this.getActivationCodeById(insertId);
  }

  /**
   * 批量创建激活码
   */
  static async createBatchActivationCodes(
    params: Omit<CreateActivationCodeParams, 'batch_id'>,
    count: number
  ): Promise<ActivationCode[]> {
    const batch_id = `batch_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`;
    const codes: ActivationCode[] = [];

    for (let i = 0; i < count; i++) {
      const code = await this.createActivationCode({
        ...params,
        batch_id
      });
      codes.push(code);
    }

    return codes;
  }

  /**
   * 根据ID获取激活码
   */
  static async getActivationCodeById(id: number): Promise<ActivationCode> {
    const code = await db.queryOne<ActivationCode>(
      `SELECT * FROM activation_codes WHERE id = ?`,
      [id]
    );

    if (!code) {
      throw new Error('Activation code not found');
    }

    return code;
  }

  /**
   * 根据激活码字符串获取激活码
   */
  static async getActivationCodeByCode(code: string): Promise<ActivationCode | null> {
    return await db.queryOne<ActivationCode>(
      `SELECT * FROM activation_codes WHERE code = ?`,
      [code]
    );
  }

  /**
   * 获取激活码列表（带分页和筛选）
   */
  static async getActivationCodesWithFilters(filters: ActivationCodeFilters): Promise<{
    codes: ActivationCode[];
    total: number;
  }> {
    const {
      page = 1,
      limit = 20,
      search,
      vip_level,
      status,
      batch_id,
      created_by,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = filters;

    const offset = (page - 1) * limit;
    const conditions: string[] = [];
    const values: any[] = [];

    // 构建查询条件
    if (search) {
      conditions.push('(code LIKE ? OR description LIKE ?)');
      const searchTerm = `%${search}%`;
      values.push(searchTerm, searchTerm);
    }

    if (vip_level) {
      conditions.push('vip_level = ?');
      values.push(vip_level);
    }

    if (status) {
      conditions.push('status = ?');
      values.push(status);
    }

    if (batch_id) {
      conditions.push('batch_id = ?');
      values.push(batch_id);
    }

    if (created_by) {
      conditions.push('created_by = ?');
      values.push(created_by);
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    const orderClause = `ORDER BY ${sort_by} ${sort_order.toUpperCase()}`;

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM activation_codes ${whereClause}`;
    const countResult = await db.queryOne<{ total: number }>(countQuery, values);
    const total = countResult?.total || 0;

    // 获取激活码列表，包含用户关联信息
    const codesQuery = `
      SELECT
        ac.*,
        u.username as user_username,
        u.email as user_email,
        u.full_name as user_full_name,
        creator.username as creator_username,
        creator.email as creator_email
      FROM activation_codes ac
      LEFT JOIN users u ON ac.used_by = u.id
      LEFT JOIN users creator ON ac.created_by = creator.id
      ${whereClause}
      ${orderClause}
      LIMIT ${limit} OFFSET ${offset}
    `;

    const rawCodes = await db.query<any>(codesQuery, values);

    // 转换数据格式，包含用户信息
    const codes: ActivationCode[] = rawCodes.map(row => ({
      id: row.id,
      code: row.code,
      vip_level: row.vip_level,
      vip_duration_days: row.vip_duration_days,
      status: row.status,
      used_by: row.used_by,
      used_at: row.used_at,
      created_by: row.created_by,
      created_at: row.created_at,
      expires_at: row.expires_at,
      description: row.description,
      batch_id: row.batch_id,
      user: row.used_by ? {
        id: row.used_by,
        username: row.user_username,
        email: row.user_email,
        full_name: row.user_full_name,
      } : undefined,
      creator: {
        id: row.created_by,
        username: row.creator_username,
        email: row.creator_email,
      }
    }));

    return { codes, total };
  }

  /**
   * 使用激活码
   */
  static async useActivationCode(code: string, userId: number, ipAddress?: string, userAgent?: string): Promise<{
    success: boolean;
    message: string;
    vip_level?: string;
    vip_duration_days?: number;
  }> {
    const activationCode = await this.getActivationCodeByCode(code);

    if (!activationCode) {
      return { success: false, message: '激活码不存在' };
    }

    if (activationCode.status !== 'active') {
      return { success: false, message: '激活码已失效' };
    }

    if (activationCode.expires_at && new Date() > activationCode.expires_at) {
      // 标记为过期
      await this.updateActivationCodeStatus(activationCode.id, 'expired');
      return { success: false, message: '激活码已过期' };
    }

    // 标记为已使用
    await db.query(
      'UPDATE activation_codes SET status = ?, used_by = ?, used_at = NOW() WHERE id = ?',
      ['used', userId, activationCode.id]
    );

    // 记录日志
    await this.logAction({
      activation_code_id: activationCode.id,
      user_id: userId,
      action: 'used',
      old_status: 'active',
      new_status: 'used',
      details: { vip_level: activationCode.vip_level, vip_duration_days: activationCode.vip_duration_days },
      ip_address: ipAddress,
      user_agent: userAgent
    });

    return {
      success: true,
      message: '激活码使用成功',
      vip_level: activationCode.vip_level,
      vip_duration_days: activationCode.vip_duration_days
    };
  }

  /**
   * 更新激活码状态
   */
  static async updateActivationCodeStatus(
    id: number,
    status: 'active' | 'used' | 'expired' | 'disabled',
    adminId?: number
  ): Promise<void> {
    const currentCode = await this.getActivationCodeById(id);
    const oldStatus = currentCode.status;

    await db.query(
      'UPDATE activation_codes SET status = ? WHERE id = ?',
      [status, id]
    );

    // 记录日志
    await this.logAction({
      activation_code_id: id,
      admin_id: adminId,
      action: status === 'disabled' ? 'disabled' : 'enabled',
      old_status: oldStatus,
      new_status: status
    });
  }

  /**
   * 更新激活码描述
   */
  static async updateDescription(id: number, description: string): Promise<void> {
    await db.query(
      'UPDATE activation_codes SET description = ? WHERE id = ?',
      [description, id]
    );
  }

  /**
   * 删除激活码
   */
  static async deleteActivationCode(id: number, adminId: number): Promise<void> {
    const code = await this.getActivationCodeById(id);

    if (code.status === 'used') {
      throw new Error('Cannot delete used activation code');
    }

    await db.query('DELETE FROM activation_codes WHERE id = ?', [id]);

    // 注意：由于外键约束，相关日志会自动删除
  }

  /**
   * 获取激活码统计信息
   */
  static async getActivationCodeStats(): Promise<ActivationCodeStats> {
    const stats = await db.queryOne<{
      total: number;
      active: number;
      used: number;
      expired: number;
      disabled: number;
      v1_count: number;
      v2_count: number;
      v3_count: number;
      v4_count: number;
    }>(`
      SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
        SUM(CASE WHEN status = 'used' THEN 1 ELSE 0 END) as used,
        SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired,
        SUM(CASE WHEN status = 'disabled' THEN 1 ELSE 0 END) as disabled,
        SUM(CASE WHEN vip_level = 'v1' THEN 1 ELSE 0 END) as v1_count,
        SUM(CASE WHEN vip_level = 'v2' THEN 1 ELSE 0 END) as v2_count,
        SUM(CASE WHEN vip_level = 'v3' THEN 1 ELSE 0 END) as v3_count,
        SUM(CASE WHEN vip_level = 'v4' THEN 1 ELSE 0 END) as v4_count
      FROM activation_codes
    `);

    return {
      total: Number(stats?.total) || 0,
      active: Number(stats?.active) || 0,
      used: Number(stats?.used) || 0,
      expired: Number(stats?.expired) || 0,
      disabled: Number(stats?.disabled) || 0,
      by_level: {
        v1: Number(stats?.v1_count) || 0,
        v2: Number(stats?.v2_count) || 0,
        v3: Number(stats?.v3_count) || 0,
        v4: Number(stats?.v4_count) || 0,
      }
    };
  }

  /**
   * 清理过期激活码
   */
  static async cleanupExpiredCodes(): Promise<number> {
    const result = await db.query(
      `UPDATE activation_codes
       SET status = 'expired'
       WHERE status = 'active' AND expires_at IS NOT NULL AND expires_at <= NOW()`
    );

    return (result as any).affectedRows || 0;
  }

  /**
   * 记录操作日志
   */
  static async logAction(params: {
    activation_code_id: number;
    user_id?: number;
    admin_id?: number;
    action: 'created' | 'used' | 'expired' | 'disabled' | 'enabled';
    old_status?: string;
    new_status?: string;
    details?: any;
    ip_address?: string;
    user_agent?: string;
  }): Promise<void> {
    await db.query(
      `INSERT INTO activation_code_logs
       (activation_code_id, user_id, admin_id, action, old_status, new_status, details, ip_address, user_agent)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        params.activation_code_id,
        params.user_id || null,
        params.admin_id || null,
        params.action,
        params.old_status || null,
        params.new_status || null,
        params.details ? JSON.stringify(params.details) : null,
        params.ip_address || null,
        params.user_agent || null
      ]
    );
  }
}
