"use client"

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Ticket, CheckCircle, XCircle, Clock, Ban } from 'lucide-react';
import { ActivationCodeStats } from '@/services/activation-code.service';

interface ActivationCodeStatsCardsProps {
  stats: ActivationCodeStats;
  loading?: boolean;
}

export function ActivationCodeStatsCards({ stats, loading }: ActivationCodeStatsCardsProps) {
  const statsData = [
    {
      title: '总激活码',
      value: stats.total,
      icon: Ticket,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: '有效激活码',
      value: stats.active,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: '已使用',
      value: stats.used,
      icon: XCircle,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
    },
    {
      title: '已过期',
      value: stats.expired,
      icon: Clock,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
    },
    {
      title: '已禁用',
      value: stats.disabled,
      icon: Ban,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ];

  const vipLevelData = [
    {
      title: 'V1激活码',
      value: stats.by_level.v1,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
    },
    {
      title: 'V2激活码',
      value: stats.by_level.v2,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'V3激活码',
      value: stats.by_level.v3,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'V4激活码',
      value: stats.by_level.v4,
      color: 'text-amber-600',
      bgColor: 'bg-amber-50',
    },
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        {/* 状态统计 */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          {Array.from({ length: 5 }).map((_, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </CardTitle>
                <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded animate-pulse mb-1"></div>
                <div className="h-3 bg-gray-200 rounded animate-pulse w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* VIP等级统计 */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded animate-pulse mb-1"></div>
                <div className="h-3 bg-gray-200 rounded animate-pulse w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 状态统计 */}
      <div>
        <h3 className="text-lg font-medium mb-4">激活码状态统计</h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          {statsData.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </CardTitle>
                  <div className={`p-2 rounded-full ${stat.bgColor}`}>
                    <Icon className={`h-4 w-4 ${stat.color}`} />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{(stat.value || 0).toLocaleString()}</div>
                  <p className="text-xs text-muted-foreground">
                    当前状态
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* VIP等级统计 */}
      <div>
        <h3 className="text-lg font-medium mb-4">VIP等级分布</h3>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {vipLevelData.map((stat, index) => (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </CardTitle>
                <div className={`w-3 h-3 rounded-full ${stat.bgColor} border-2 border-current ${stat.color}`}></div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{(stat.value || 0).toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  激活码数量
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
