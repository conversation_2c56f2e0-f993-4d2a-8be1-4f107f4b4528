"use client"

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, X } from 'lucide-react';
import { UserListParams } from '@/services/user.service';

interface UserFiltersProps {
  filters: UserListParams;
  onFiltersChange: (filters: UserListParams) => void;
  onReset: () => void;
}

export function UserFilters({ filters, onFiltersChange, onReset }: UserFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleFilterChange = (key: keyof UserListParams, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
      page: 1, // Reset to first page when filters change
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.role) count++;
    if (filters.vip_level) count++;
    if (typeof filters.is_active === 'boolean') count++;
    if (typeof filters.is_banned === 'boolean') count++;
    if (filters.registration_source) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  const clearFilter = (key: keyof UserListParams) => {
    const newFilters = { ...filters };
    delete newFilters[key];
    onFiltersChange(newFilters);
  };

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索用户名、邮箱或姓名..."
            value={filters.search || ''}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="pl-8"
          />
        </div>
        
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="relative">
              <Filter className="mr-2 h-4 w-4" />
              筛选
              {activeFiltersCount > 0 && (
                <Badge 
                  variant="destructive" 
                  className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 text-xs"
                >
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">筛选条件</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    onReset();
                    setIsOpen(false);
                  }}
                >
                  重置
                </Button>
              </div>

              {/* Role Filter */}
              <div className="space-y-2">
                <Label>角色</Label>
                <Select
                  value={filters.role || 'all'}
                  onValueChange={(value) => handleFilterChange('role', value === 'all' ? undefined : value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择角色" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部角色</SelectItem>
                    <SelectItem value="admin">管理员</SelectItem>
                    <SelectItem value="moderator">版主</SelectItem>
                    <SelectItem value="user">普通用户</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* VIP Level Filter */}
              <div className="space-y-2">
                <Label>VIP等级</Label>
                <Select
                  value={filters.vip_level || 'all'}
                  onValueChange={(value) => handleFilterChange('vip_level', value === 'all' ? undefined : value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择VIP等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部等级</SelectItem>
                    <SelectItem value="none">普通用户</SelectItem>
                    <SelectItem value="v1">VIP V1</SelectItem>
                    <SelectItem value="v2">VIP V2</SelectItem>
                    <SelectItem value="v3">VIP V3</SelectItem>
                    <SelectItem value="v4">VIP V4</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Active Status Filter */}
              <div className="space-y-2">
                <Label>账户状态</Label>
                <Select
                  value={
                    typeof filters.is_active === 'boolean'
                      ? filters.is_active.toString()
                      : 'all'
                  }
                  onValueChange={(value) =>
                    handleFilterChange('is_active', value === 'all' ? undefined : value === 'true')
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="true">活跃</SelectItem>
                    <SelectItem value="false">非活跃</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Ban Status Filter */}
              <div className="space-y-2">
                <Label>封禁状态</Label>
                <Select
                  value={
                    typeof filters.is_banned === 'boolean'
                      ? filters.is_banned.toString()
                      : 'all'
                  }
                  onValueChange={(value) =>
                    handleFilterChange('is_banned', value === 'all' ? undefined : value === 'true')
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择封禁状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="false">正常</SelectItem>
                    <SelectItem value="true">已封禁</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Registration Source Filter */}
              <div className="space-y-2">
                <Label>注册来源</Label>
                <Select
                  value={filters.registration_source || 'all'}
                  onValueChange={(value) => handleFilterChange('registration_source', value === 'all' ? undefined : value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择注册来源" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部来源</SelectItem>
                    <SelectItem value="web">网页端</SelectItem>
                    <SelectItem value="mobile">移动端</SelectItem>
                    <SelectItem value="api">API</SelectItem>
                    <SelectItem value="admin">管理员创建</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.search && (
            <Badge variant="secondary" className="gap-1">
              搜索: {filters.search}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => clearFilter('search')}
              />
            </Badge>
          )}
          {filters.role && (
            <Badge variant="secondary" className="gap-1">
              角色: {filters.role}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => clearFilter('role')}
              />
            </Badge>
          )}
          {filters.vip_level && (
            <Badge variant="secondary" className="gap-1">
              VIP: {filters.vip_level}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => clearFilter('vip_level')}
              />
            </Badge>
          )}
          {typeof filters.is_active === 'boolean' && (
            <Badge variant="secondary" className="gap-1">
              状态: {filters.is_active ? '活跃' : '非活跃'}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => clearFilter('is_active')}
              />
            </Badge>
          )}
          {typeof filters.is_banned === 'boolean' && (
            <Badge variant="secondary" className="gap-1">
              封禁: {filters.is_banned ? '已封禁' : '正常'}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => clearFilter('is_banned')}
              />
            </Badge>
          )}
          {filters.registration_source && (
            <Badge variant="secondary" className="gap-1">
              来源: {filters.registration_source}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => clearFilter('registration_source')}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
