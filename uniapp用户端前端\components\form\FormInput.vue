<template>
  <view class="form-input-wrapper">
    <!-- 标签 -->
    <view v-if="label" class="form-label">
      <text>{{ label }}</text>
      <text v-if="required" class="required-mark">*</text>
    </view>

    <!-- 输入框容器 -->
    <view class="input-container" :class="{ 'error': hasError, 'focused': isFocused }">
      <!-- 前缀图标 -->
      <view v-if="prefixIcon" class="prefix-icon">
        <text class="icon">{{ prefixIcon }}</text>
      </view>

      <!-- 输入框 -->
      <input 
        class="form-input"
        :type="inputType"
        :value="value"
        :placeholder="placeholder"
        :disabled="disabled"
        :maxlength="maxlength"
        :style="inputStyle"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @confirm="handleConfirm"
      />

      <!-- 后缀图标/操作按钮 -->
      <view v-if="suffixIcon || showClear || isPassword" class="suffix-actions">
        <!-- 清除按钮 -->
        <view 
          v-if="showClear && value && !disabled" 
          class="action-btn clear-btn"
          @tap="handleClear"
        >
          <text class="icon">✕</text>
        </view>

        <!-- 密码显示切换 -->
        <view 
          v-if="isPassword" 
          class="action-btn password-toggle"
          @tap="togglePassword"
        >
          <text class="icon">{{ showPassword ? '👁️' : '👁️‍🗨️' }}</text>
        </view>

        <!-- 后缀图标 -->
        <view v-if="suffixIcon" class="suffix-icon">
          <text class="icon">{{ suffixIcon }}</text>
        </view>
      </view>
    </view>

    <!-- 错误提示 -->
    <view v-if="hasError" class="error-text">
      <text>{{ errorMessage }}</text>
    </view>

    <!-- 帮助文本 -->
    <view v-if="helpText && !hasError" class="help-text">
      <text>{{ helpText }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'FormInput',
  
  props: {
    // 基础属性
    value: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'text'
    },
    required: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: [Number, String],
      default: -1
    },
    
    // 样式属性
    prefixIcon: {
      type: String,
      default: ''
    },
    suffixIcon: {
      type: String,
      default: ''
    },
    showClear: {
      type: Boolean,
      default: false
    },
    
    // 验证属性
    error: {
      type: String,
      default: ''
    },
    helpText: {
      type: String,
      default: ''
    },
    
    // 特殊功能
    autoUppercase: {
      type: Boolean,
      default: false
    },
    inputStyle: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      isFocused: false,
      showPassword: false
    };
  },

  computed: {
    // 是否为密码输入框
    isPassword() {
      return this.type === 'password';
    },

    // 实际输入框类型
    inputType() {
      if (this.isPassword) {
        return this.showPassword ? 'text' : 'password';
      }
      return this.type;
    },

    // 是否有错误
    hasError() {
      return !!this.error;
    },

    // 错误信息
    errorMessage() {
      return this.error;
    }
  },

  methods: {
    // 处理输入
    handleInput(e) {
      let value = e.detail.value;
      
      // 自动转大写
      if (this.autoUppercase) {
        value = value.toUpperCase();
      }
      
      this.$emit('input', value);
      this.$emit('change', value);
    },

    // 处理聚焦
    handleFocus(e) {
      this.isFocused = true;
      this.$emit('focus', e);
    },

    // 处理失焦
    handleBlur(e) {
      this.isFocused = false;
      this.$emit('blur', e);
    },

    // 处理确认
    handleConfirm(e) {
      this.$emit('confirm', e);
    },

    // 清除内容
    handleClear() {
      this.$emit('input', '');
      this.$emit('change', '');
      this.$emit('clear');
    },

    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword;
      this.$emit('password-toggle', this.showPassword);
    }
  }
};
</script>

<style scoped>
.form-input-wrapper {
  margin-bottom: 24rpx;
}

.form-label {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 24rpx;
  font-weight: 500;
  color: #000000;
  letter-spacing: 1.2rpx;
  line-height: 32.86rpx;
  margin-bottom: 18rpx;
  display: flex;
  align-items: center;
}

.required-mark {
  color: #F2282D;
  margin-left: 4rpx;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
  border: 2.34rpx solid #D1D1D1;
  border-radius: 200rpx;
  background: #ffffff;
  transition: all 0.3s ease;
  overflow: hidden;
  height: 108rpx;
  padding: 0 46rpx;
}

.input-container.focused {
  border-color: #F2282D;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(242, 40, 45, 0.1);
}

.input-container.error {
  border-color: #F2282D;
  background: #fff5f5;
}

.prefix-icon {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.prefix-icon .icon {
  font-size: 24rpx;
  color: #666;
}

.form-input {
  flex: 1;
  height: 100%;
  padding: 0;
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  background: transparent;
  color: #000000;
  letter-spacing: 1.2rpx;
}

.form-input::placeholder {
  color: #989898;
  font-weight: 500;
}

.suffix-actions {
  display: flex;
  align-items: center;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.action-btn {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
  transition: all 0.2s ease;
}

.action-btn:active {
  background: rgba(0, 0, 0, 0.05);
}

.clear-btn .icon {
  color: #ccc;
  font-size: 14rpx;
}

.password-toggle .icon {
  color: #999;
  font-size: 20rpx;
}

.suffix-icon {
  width: 36rpx;
  height: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 20rpx;
}

.error-text {
  color: #F2282D;
  font-size: 14rpx;
  margin-top: 8rpx;
  line-height: 1.4;
  text-align: center;
}

.help-text {
  color: #999;
  font-size: 12rpx;
  margin-top: 5rpx;
  line-height: 1.4;
}

/* 密码输入框特殊样式 */
.form-input[type="password"] {
  font-size: 36rpx;
  letter-spacing: 0.9rpx;
  font-weight: normal;
}

.icon {
  display: inline-block;
}
</style>
