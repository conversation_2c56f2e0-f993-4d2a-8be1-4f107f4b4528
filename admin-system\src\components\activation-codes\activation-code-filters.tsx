"use client"

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, X } from 'lucide-react';
import { ActivationCodeListParams, VIP_LEVEL_CONFIG } from '@/services/activation-code.service';

interface ActivationCodeFiltersProps {
  filters: ActivationCodeListParams;
  onFiltersChange: (filters: ActivationCodeListParams) => void;
  onReset: () => void;
}

export function ActivationCodeFilters({ filters, onFiltersChange, onReset }: ActivationCodeFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleFilterChange = (key: keyof ActivationCodeListParams, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
      page: 1, // Reset to first page when filters change
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.vip_level) count++;
    if (filters.status) count++;
    if (filters.batch_id) count++;
    if (filters.created_by) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  const clearFilter = (key: keyof ActivationCodeListParams) => {
    const newFilters = { ...filters };
    delete newFilters[key];
    onFiltersChange(newFilters);
  };

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索激活码或描述..."
            value={filters.search || ''}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="pl-8"
          />
        </div>
        
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant="outline" className="relative">
              <Filter className="mr-2 h-4 w-4" />
              筛选
              {activeFiltersCount > 0 && (
                <Badge 
                  variant="destructive" 
                  className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 text-xs"
                >
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80" align="end">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">筛选条件</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    onReset();
                    setIsOpen(false);
                  }}
                >
                  重置
                </Button>
              </div>

              {/* VIP Level Filter */}
              <div className="space-y-2">
                <Label>VIP等级</Label>
                <Select
                  value={filters.vip_level || ''}
                  onValueChange={(value) => handleFilterChange('vip_level', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择VIP等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部等级</SelectItem>
                    {Object.entries(VIP_LEVEL_CONFIG).map(([key, config]) => (
                      <SelectItem key={key} value={key}>
                        {config.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <Label>状态</Label>
                <Select
                  value={filters.status || ''}
                  onValueChange={(value) => handleFilterChange('status', value || undefined)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">全部状态</SelectItem>
                    <SelectItem value="active">有效</SelectItem>
                    <SelectItem value="used">已使用</SelectItem>
                    <SelectItem value="expired">已过期</SelectItem>
                    <SelectItem value="disabled">已禁用</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Batch ID Filter */}
              <div className="space-y-2">
                <Label>批次ID</Label>
                <Input
                  placeholder="输入批次ID"
                  value={filters.batch_id || ''}
                  onChange={(e) => handleFilterChange('batch_id', e.target.value || undefined)}
                />
              </div>

              {/* Sort Options */}
              <div className="space-y-2">
                <Label>排序方式</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Select
                    value={filters.sort_by || 'created_at'}
                    onValueChange={(value) => handleFilterChange('sort_by', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="排序字段" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="created_at">创建时间</SelectItem>
                      <SelectItem value="code">激活码</SelectItem>
                      <SelectItem value="vip_level">VIP等级</SelectItem>
                      <SelectItem value="status">状态</SelectItem>
                      <SelectItem value="used_at">使用时间</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select
                    value={filters.sort_order || 'desc'}
                    onValueChange={(value) => handleFilterChange('sort_order', value as 'asc' | 'desc')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="排序方向" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="desc">降序</SelectItem>
                      <SelectItem value="asc">升序</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.search && (
            <Badge variant="secondary" className="gap-1">
              搜索: {filters.search}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => clearFilter('search')}
              />
            </Badge>
          )}
          {filters.vip_level && (
            <Badge variant="secondary" className="gap-1">
              等级: {VIP_LEVEL_CONFIG[filters.vip_level as keyof typeof VIP_LEVEL_CONFIG]?.label || filters.vip_level}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => clearFilter('vip_level')}
              />
            </Badge>
          )}
          {filters.status && (
            <Badge variant="secondary" className="gap-1">
              状态: {filters.status === 'active' ? '有效' : 
                     filters.status === 'used' ? '已使用' : 
                     filters.status === 'expired' ? '已过期' : 
                     filters.status === 'disabled' ? '已禁用' : filters.status}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => clearFilter('status')}
              />
            </Badge>
          )}
          {filters.batch_id && (
            <Badge variant="secondary" className="gap-1">
              批次: {filters.batch_id}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => clearFilter('batch_id')}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
