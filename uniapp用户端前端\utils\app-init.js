/**
 * 应用初始化工具
 * 处理应用启动时的初始化逻辑
 */

import { AuthHelper } from './auth-helper.js';
import { ToastManager } from './toast-manager.js';

// 应用初始化管理器
export class AppInitializer {
  // 初始化应用
  static async initApp() {
    console.log('应用初始化开始...');
    
    try {
      // 1. 检查登录状态
      await this.checkAuthStatus();
      
      // 2. 初始化其他服务
      await this.initServices();
      
      console.log('应用初始化完成');
      return { success: true };
      
    } catch (error) {
      console.error('应用初始化失败:', error);
      ToastManager.error('应用初始化失败');
      return { success: false, error };
    }
  }

  // 检查认证状态
  static async checkAuthStatus() {
    try {
      // 自动登录检查
      const isValid = await AuthHelper.autoLoginCheck();
      
      if (isValid) {
        console.log('用户已登录，状态有效');
      } else {
        console.log('用户未登录或状态无效');
      }
      
      return isValid;
    } catch (error) {
      console.error('检查认证状态失败:', error);
      return false;
    }
  }

  // 初始化服务
  static async initServices() {
    // 这里可以初始化其他服务
    // 比如：推送服务、统计服务等
    console.log('初始化其他服务...');
  }

  // 检查页面访问权限
  static checkPageAccess(pagePath) {
    // 需要登录的页面列表
    const authRequiredPages = [
      '/pages/profile/profile',
      '/pages/user-profile/user-profile',
      '/pages/save/save'
    ];

    // 检查是否需要登录
    if (authRequiredPages.includes(pagePath)) {
      if (!AuthHelper.isLoggedIn()) {
        ToastManager.error('请先登录');
        AuthHelper.redirectToLogin();
        return false;
      }
    }

    return true;
  }

  // VIP页面访问检查
  static checkVipPageAccess(pagePath, requiredLevel = 'v1') {
    // VIP页面列表
    const vipPages = {
      '/pages/vip-feature/vip-feature': 'v1',
      '/pages/premium/premium': 'v2'
    };

    const pageVipLevel = vipPages[pagePath];
    if (pageVipLevel) {
      return AuthHelper.checkVipAccess(pageVipLevel);
    }

    return true;
  }

  // 处理页面跳转
  static handlePageNavigation(url, options = {}) {
    try {
      // 解析URL
      const [pagePath, query] = url.split('?');
      
      // 检查页面访问权限
      if (!this.checkPageAccess(pagePath)) {
        return false;
      }

      // 检查VIP权限
      if (!this.checkVipPageAccess(pagePath)) {
        return false;
      }

      // 执行跳转
      const navigateMethod = options.reLaunch ? 'reLaunch' : 
                           options.redirectTo ? 'redirectTo' : 
                           options.switchTab ? 'switchTab' : 'navigateTo';

      uni[navigateMethod]({
        url,
        success: options.success,
        fail: options.fail,
        complete: options.complete
      });

      return true;
    } catch (error) {
      console.error('页面跳转失败:', error);
      ToastManager.error('页面跳转失败');
      return false;
    }
  }
}

// 页面生命周期混入
export const pageLifecycleMixin = {
  onLoad(options) {
    // 检查页面访问权限
    const currentPages = getCurrentPages();
    const currentPage = currentPages[currentPages.length - 1];
    const pagePath = '/' + currentPage.route;
    
    AppInitializer.checkPageAccess(pagePath);
  },

  onShow() {
    // 页面显示时检查登录状态
    if (this.requireAuth && !AuthHelper.isLoggedIn()) {
      AuthHelper.redirectToLogin();
    }
  }
};

// 导出便捷方法
export const appUtils = {
  init: AppInitializer.initApp,
  checkAuth: AppInitializer.checkAuthStatus,
  checkPageAccess: AppInitializer.checkPageAccess,
  checkVipAccess: AppInitializer.checkVipPageAccess,
  navigate: AppInitializer.handlePageNavigation
};

export default AppInitializer;
