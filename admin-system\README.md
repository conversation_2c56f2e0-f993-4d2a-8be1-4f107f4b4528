# Admin System

A modern, full-stack admin dashboard built with Next.js 15, TypeScript, shadcn/ui, and MySQL.

## 🚀 Features

- **Modern Tech Stack**: Next.js 15 with App Router, TypeScript, Tailwind CSS
- **Beautiful UI**: shadcn/ui components with dark/light theme support
- **Authentication**: JWT-based authentication with secure session management
- **Database**: MySQL with connection pooling and prepared statements
- **Security**: Password hashing, rate limiting, audit logging
- **Role-based Access**: Admin, moderator, and user roles
- **Responsive Design**: Mobile-first responsive design
- **API Routes**: RESTful API with proper error handling and validation

## 🛠️ Tech Stack

### Frontend
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Beautiful and accessible UI components
- **Lucide React** - Beautiful icons
- **next-themes** - Theme switching support

### Backend
- **Next.js API Routes** - Serverless API endpoints
- **MySQL2** - MySQL database driver with Promise support
- **bcryptjs** - Password hashing
- **jsonwebtoken** - JWT token generation and verification
- **crypto-js** - Additional cryptographic utilities

### Database
- **MySQL** - Relational database
- **Connection Pooling** - Efficient database connections
- **Prepared Statements** - SQL injection prevention

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v18 or higher)
- **npm** or **yarn** or **pnpm**
- **MySQL** (v8.0 or higher)

## 🚀 Quick Start

### 1. Install dependencies

```bash
npm install
```

### 2. Set up environment variables

Create a `.env.local` file in the root directory:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_mysql_password
DB_NAME=admin_system

# JWT Secret (change this in production)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key

# API Configuration
API_BASE_URL=http://localhost:3000/api

# Environment
NODE_ENV=development
```

### 3. Set up the database

Make sure MySQL is running, then initialize the database:

```bash
npm run init-db
```

This will:
- Create the database and tables
- Create a default admin user
- Insert sample data
- Set up system settings

### 4. Start the development server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🔐 Default Credentials

After running the database initialization, you can log in with:

- **Email**: `<EMAIL>`
- **Password**: `password123`

**⚠️ Important**: Change the default admin password in production!
