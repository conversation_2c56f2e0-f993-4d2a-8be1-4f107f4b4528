<template>
  <button 
    class="form-button"
    :class="[
      `form-button--${type}`,
      `form-button--${size}`,
      {
        'form-button--block': block,
        'form-button--loading': loading,
        'form-button--disabled': disabled || loading
      }
    ]"
    :disabled="disabled || loading"
    @tap="handleClick"
  >
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-wrapper">
      <view class="loading-spinner"></view>
      <text v-if="loadingText" class="button-text">{{ loadingText }}</text>
    </view>
    
    <!-- 正常状态 -->
    <view v-else class="button-content">
      <!-- 前缀图标 -->
      <view v-if="prefixIcon" class="prefix-icon">
        <text class="icon">{{ prefixIcon }}</text>
      </view>
      
      <!-- 按钮文字 -->
      <text class="button-text">{{ text }}</text>
      
      <!-- 后缀图标 -->
      <view v-if="suffixIcon" class="suffix-icon">
        <text class="icon">{{ suffixIcon }}</text>
      </view>
    </view>
  </button>
</template>

<script>
export default {
  name: 'FormButton',
  
  props: {
    // 基础属性
    text: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'primary',
      validator: value => ['primary', 'secondary', 'outline', 'text', 'danger'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    block: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    },
    loadingText: {
      type: String,
      default: ''
    },
    
    // 图标
    prefixIcon: {
      type: String,
      default: ''
    },
    suffixIcon: {
      type: String,
      default: ''
    }
  },
  
  methods: {
    handleClick(e) {
      if (this.disabled || this.loading) {
        return;
      }
      this.$emit('click', e);
    }
  }
};
</script>

<style scoped>
.form-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 200rpx;
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.form-button:active {
  transform: scale(0.98);
}

/* 按钮类型 */
.form-button--primary {
  background: #F2282D;
  color: #ffffff;
}

.form-button--primary:hover {
  background: #d91e22;
}

.form-button--secondary {
  background: #f8f8f8;
  color: #333333;
  border: 2rpx solid #e0e0e0;
}

.form-button--secondary:hover {
  background: #f0f0f0;
}

.form-button--outline {
  background: transparent;
  color: #F2282D;
  border: 4rpx solid #F2282D;
}

.form-button--outline:hover {
  background: #F2282D;
  color: #ffffff;
}

.form-button--text {
  background: transparent;
  color: #F2282D;
}

.form-button--text:hover {
  background: rgba(242, 40, 45, 0.1);
}

.form-button--danger {
  background: #ff4757;
  color: #ffffff;
}

.form-button--danger:hover {
  background: #ff3838;
}

/* 按钮尺寸 */
.form-button--small {
  height: 36rpx;
  padding: 0 16rpx;
  font-size: 14rpx;
}

.form-button--medium {
  height: 54rpx;
  padding: 0 24rpx;
  font-size: 16rpx;
}

.form-button--large {
  height: 108rpx;
  padding: 0 32rpx;
  font-size: 32rpx;
}

/* 块级按钮 */
.form-button--block {
  width: 100%;
}

/* 禁用状态 */
.form-button--disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #ccc !important;
  color: #999 !important;
  border-color: #ccc !important;
}

.form-button--disabled:active {
  transform: none;
}

/* 加载状态 */
.form-button--loading {
  opacity: 0.8;
  cursor: not-allowed;
}

.form-button--loading:active {
  transform: none;
}

/* 按钮内容 */
.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.button-text {
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
}

.prefix-icon,
.suffix-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.prefix-icon .icon,
.suffix-icon .icon {
  font-size: 1.2em;
}

/* 加载动画 */
.loading-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.loading-spinner {
  width: 16rpx;
  height: 16rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-top: 2rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 小尺寸加载动画 */
.form-button--small .loading-spinner {
  width: 12rpx;
  height: 12rpx;
  border-width: 1.5rpx;
}

/* 大尺寸加载动画 */
.form-button--large .loading-spinner {
  width: 24rpx;
  height: 24rpx;
  border-width: 3rpx;
}
</style>
