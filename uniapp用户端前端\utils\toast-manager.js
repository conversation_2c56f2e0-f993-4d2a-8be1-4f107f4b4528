/**
 * Toast消息管理工具
 * 提供统一的消息提示功能
 */

// Toast管理器
export class ToastManager {
  // 显示成功消息
  static success(message, duration = 2000) {
    uni.showToast({
      title: message,
      icon: 'success',
      duration: duration,
      mask: false
    });
  }

  // 显示错误消息
  static error(message, duration = 3000) {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: duration,
      mask: false
    });
  }

  // 显示警告消息
  static warning(message, duration = 2500) {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: duration,
      mask: false
    });
  }

  // 显示信息消息
  static info(message, duration = 2000) {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: duration,
      mask: false
    });
  }

  // 显示加载中
  static loading(message = '加载中...', mask = true) {
    uni.showLoading({
      title: message,
      mask: mask
    });
  }

  // 隐藏加载中
  static hideLoading() {
    uni.hideLoading();
  }

  // 显示模态对话框
  static confirm(options = {}) {
    const defaultOptions = {
      title: '提示',
      content: '确定要执行此操作吗？',
      showCancel: true,
      confirmText: '确定',
      cancelText: '取消',
      confirmColor: '#007aff',
      cancelColor: '#000000'
    };

    const finalOptions = { ...defaultOptions, ...options };

    return new Promise((resolve, reject) => {
      uni.showModal({
        ...finalOptions,
        success: (res) => {
          if (res.confirm) {
            resolve(true);
          } else if (res.cancel) {
            resolve(false);
          }
        },
        fail: (error) => {
          reject(error);
        }
      });
    });
  }

  // 显示操作菜单
  static actionSheet(options = {}) {
    const defaultOptions = {
      itemList: [],
      itemColor: '#000000'
    };

    const finalOptions = { ...defaultOptions, ...options };

    return new Promise((resolve, reject) => {
      uni.showActionSheet({
        ...finalOptions,
        success: (res) => {
          resolve(res.tapIndex);
        },
        fail: (error) => {
          if (error.errMsg !== 'showActionSheet:fail cancel') {
            reject(error);
          } else {
            resolve(-1); // 用户取消
          }
        }
      });
    });
  }

  // 显示自定义消息（带图标）
  static custom(message, icon = 'none', duration = 2000) {
    uni.showToast({
      title: message,
      icon: icon,
      duration: duration,
      mask: false
    });
  }

  // 显示网络错误消息
  static networkError(message = '网络连接失败，请检查网络设置') {
    this.error(message, 3000);
  }

  // 显示权限错误消息
  static permissionError(message = '权限不足，请联系管理员') {
    this.error(message, 3000);
  }

  // 显示验证错误消息
  static validationError(message = '输入信息有误，请检查后重试') {
    this.warning(message, 2500);
  }
}

// 导出便捷方法
export const toast = {
  success: ToastManager.success,
  error: ToastManager.error,
  warning: ToastManager.warning,
  info: ToastManager.info,
  loading: ToastManager.loading,
  hideLoading: ToastManager.hideLoading,
  confirm: ToastManager.confirm,
  actionSheet: ToastManager.actionSheet,
  custom: ToastManager.custom,
  networkError: ToastManager.networkError,
  permissionError: ToastManager.permissionError,
  validationError: ToastManager.validationError
};

export default ToastManager;
