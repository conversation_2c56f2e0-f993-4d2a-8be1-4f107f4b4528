import { NextRequest } from 'next/server';
import { ActivationCodeManager } from '@/lib/activation-codes';
import { 
  successResponse, 
  errorResponse, 
  logAuditEvent,
  withAdminAccess
} from '@/lib/api-utils';

// GET /api/activation-codes/stats - 获取激活码统计信息 (仅管理员)
export async function GET(request: NextRequest) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const stats = await ActivationCodeManager.getActivationCodeStats();

      await logAuditEvent({
        userId: user.id,
        action: 'ACTIVATION_CODE_STATS_VIEWED',
        resource: 'activation_codes',
        details: { stats },
      });

      return successResponse(stats, '激活码统计信息获取成功');
    } catch (error) {
      console.error('Get activation code stats error:', error);
      return errorResponse('获取激活码统计信息失败', 500);
    }
  });
}
