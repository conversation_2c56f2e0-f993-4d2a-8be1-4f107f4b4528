-- 激活码表设计
-- 用于管理VIP激活码的生成、使用和用户关联

CREATE TABLE IF NOT EXISTS activation_codes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  
  -- 激活码基本信息
  code VARCHAR(32) UNIQUE NOT NULL COMMENT '激活码（唯一）',
  vip_level ENUM('v1', 'v2', 'v3', 'v4') NOT NULL COMMENT 'VIP等级',
  vip_duration_days INT NOT NULL DEFAULT 30 COMMENT 'VIP有效期（天数）',
  
  -- 状态管理
  status ENUM('active', 'used', 'expired', 'disabled') DEFAULT 'active' COMMENT '激活码状态',
  
  -- 使用信息
  used_by INT NULL COMMENT '使用者用户ID',
  used_at TIMESTAMP NULL COMMENT '使用时间',
  
  -- 创建信息
  created_by INT NOT NULL COMMENT '创建者（管理员ID）',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  -- 过期时间
  expires_at TIMESTAMP NULL COMMENT '激活码过期时间',
  
  -- 备注信息
  description TEXT NULL COMMENT '备注说明',
  batch_id VARCHAR(50) NULL COMMENT '批次ID（用于批量生成）',
  
  -- 索引
  INDEX idx_code (code),
  INDEX idx_vip_level (vip_level),
  INDEX idx_status (status),
  INDEX idx_used_by (used_by),
  INDEX idx_created_by (created_by),
  INDEX idx_batch_id (batch_id),
  INDEX idx_created_at (created_at),
  INDEX idx_expires_at (expires_at),
  
  -- 外键约束
  FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP激活码表';

-- 创建激活码使用记录表（用于审计）
CREATE TABLE IF NOT EXISTS activation_code_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  
  -- 关联信息
  activation_code_id INT NOT NULL COMMENT '激活码ID',
  user_id INT NULL COMMENT '用户ID',
  admin_id INT NULL COMMENT '管理员ID',
  
  -- 操作信息
  action ENUM('created', 'used', 'expired', 'disabled', 'enabled') NOT NULL COMMENT '操作类型',
  old_status ENUM('active', 'used', 'expired', 'disabled') NULL COMMENT '原状态',
  new_status ENUM('active', 'used', 'expired', 'disabled') NULL COMMENT '新状态',
  
  -- 详细信息
  details JSON NULL COMMENT '操作详情',
  ip_address VARCHAR(45) NULL COMMENT 'IP地址',
  user_agent TEXT NULL COMMENT '用户代理',
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  
  -- 索引
  INDEX idx_activation_code_id (activation_code_id),
  INDEX idx_user_id (user_id),
  INDEX idx_admin_id (admin_id),
  INDEX idx_action (action),
  INDEX idx_created_at (created_at),
  
  -- 外键约束
  FOREIGN KEY (activation_code_id) REFERENCES activation_codes(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE SET NULL
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='激活码操作日志表';
