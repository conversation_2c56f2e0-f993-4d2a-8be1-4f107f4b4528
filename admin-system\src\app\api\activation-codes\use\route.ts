import { NextRequest } from 'next/server';
import { ActivationCodeManager } from '@/lib/activation-codes';
import { UserManager } from '@/lib/auth';
import {
  withUserAccess,
  successResponse,
  errorResponse,
  validateRequest,
  logAuditEvent,
  getClientIP
} from '@/lib/api-utils';

// POST /api/activation-codes/use - 使用激活码 (用户端)
export async function POST(request: NextRequest) {
  return withUserAccess()(request, async (req, user, session, clientInfo) => {
    try {
      const body = await request.json();

      // 验证请求数据
      const { isValid, errors, data } = validateRequest<{
        code: string;
      }>(body, {
        code: (value) => {
          if (!value || typeof value !== 'string' || value.trim().length === 0) {
            return 'Activation code is required';
          }
          // 验证激活码格式 (XXXX-XXXX-XXXX-XXXX)
          const codePattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
          if (!codePattern.test(value.trim().toUpperCase())) {
            return 'Invalid activation code format';
          }
          return null;
        },
      });

      if (!isValid) {
        return errorResponse('Validation failed', 400, 'VALIDATION_ERROR', errors);
      }

      const { code } = data;
      const normalizedCode = code.trim().toUpperCase();
      const clientIP = getClientIP(request);
      const userAgent = request.headers.get('user-agent') || undefined;

      // 使用激活码
      const result = await ActivationCodeManager.useActivationCode(
        normalizedCode,
        user.id,
        clientIP,
        userAgent
      );

      if (!result.success) {
        await logAuditEvent({
          userId: user.id,
          action: 'ACTIVATION_CODE_USE_FAILED',
          resource: 'activation_codes',
          details: {
            code: normalizedCode,
            reason: result.message
          },
          ipAddress: clientIP,
          userAgent
        });

        return errorResponse(result.message, 400, 'ACTIVATION_FAILED');
      }

      // 如果激活码使用成功，更新用户的VIP状态
      if (result.vip_level && result.vip_duration_days) {
        // 计算VIP过期时间
        const vipExpiresAt = new Date();
        vipExpiresAt.setDate(vipExpiresAt.getDate() + result.vip_duration_days);

        // 直接使用激活码的VIP等级
        const vipLevel = result.vip_level as 'v1' | 'v2' | 'v3' | 'v4';

        // 更新用户VIP等级
        await UserManager.setVipLevel(user.id, vipLevel, vipExpiresAt);

        await logAuditEvent({
          userId: user.id,
          action: 'VIP_ACTIVATED_BY_CODE',
          resource: 'users',
          resourceId: user.id.toString(),
          details: {
            activation_code: normalizedCode,
            vip_level: result.vip_level,
            vip_duration_days: result.vip_duration_days,
            vip_expires_at: vipExpiresAt.toISOString()
          },
          ipAddress: clientIP,
          userAgent
        });
      }

      await logAuditEvent({
        userId: user.id,
        action: 'ACTIVATION_CODE_USED',
        resource: 'activation_codes',
        details: {
          code: normalizedCode,
          vip_level: result.vip_level,
          vip_duration_days: result.vip_duration_days
        },
        ipAddress: clientIP,
        userAgent
      });

      return successResponse({
        vip_level: result.vip_level,
        vip_duration_days: result.vip_duration_days,
        message: result.message
      }, 'Activation code used successfully');

    } catch (error) {
      console.error('Use activation code error:', error);
      return errorResponse('Failed to use activation code', 500);
    }
  });
}
