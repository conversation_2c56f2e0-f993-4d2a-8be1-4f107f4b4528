import mysql from 'mysql2/promise';

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'admin_system',
  waitForConnections: true,
  connectionLimit: 10,
  maxIdle: 10,
  idleTimeout: 60000,
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 0,
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Database connection wrapper
export class Database {
  private static instance: Database;
  private pool: mysql.Pool;

  private constructor() {
    this.pool = pool;
  }

  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database();
    }
    return Database.instance;
  }

  // Execute query with parameters
  async query<T = unknown>(sql: string, params?: unknown[]): Promise<T[]> {
    try {
      const [rows] = await this.pool.execute(sql, params);
      return rows as T[];
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }

  // Execute query and return first result
  async queryOne<T = unknown>(sql: string, params?: unknown[]): Promise<T | null> {
    const results = await this.query<T>(sql, params);
    return results.length > 0 ? results[0] : null;
  }

  // Execute transaction
  async transaction<T>(callback: (connection: mysql.PoolConnection) => Promise<T>): Promise<T> {
    const connection = await this.pool.getConnection();
    try {
      await connection.beginTransaction();
      const result = await callback(connection);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  // Close all connections
  async close(): Promise<void> {
    await this.pool.end();
  }
}

// Export singleton instance
export const db = Database.getInstance();

// Database initialization script
export async function initializeDatabase() {
  try {
    // Create users table
    await db.query(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(100),
        role ENUM('admin', 'user', 'moderator') DEFAULT 'user',
        avatar_url VARCHAR(255),
        is_active BOOLEAN DEFAULT true,
        is_banned BOOLEAN DEFAULT false,
        ban_reason TEXT NULL,
        ban_expires_at TIMESTAMP NULL,
        banned_by INT NULL,
        banned_at TIMESTAMP NULL,
        vip_level ENUM('none', 'v1', 'v2', 'v3', 'v4') DEFAULT 'none',
        vip_expires_at TIMESTAMP NULL,
        registration_source ENUM('web', 'mobile', 'api', 'admin') DEFAULT 'web',
        phone VARCHAR(20) NULL,
        gender ENUM('male', 'female', 'other') NULL,
        birth_date DATE NULL,
        last_login TIMESTAMP NULL,
        login_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (banned_by) REFERENCES users(id) ON DELETE SET NULL
      )
    `);

    // Create sessions table
    await db.query(`
      CREATE TABLE IF NOT EXISTS sessions (
        id VARCHAR(255) PRIMARY KEY,
        user_id INT NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    // Create audit_logs table
    await db.query(`
      CREATE TABLE IF NOT EXISTS audit_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        action VARCHAR(100) NOT NULL,
        resource VARCHAR(100),
        resource_id VARCHAR(100),
        details JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `);

    // Create system_settings table
    await db.query(`
      CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        description TEXT,
        is_public BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Run database migrations
    await runMigrations();

    console.log('Database tables initialized successfully');
  } catch (error) {
    console.error('Database initialization error:', error);
    throw error;
  }
}

// Database migrations
export async function runMigrations() {
  try {
    // Check if migrations table exists
    await db.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        migration_name VARCHAR(255) UNIQUE NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Migration 1: Add new user fields
    const migration1 = 'add_user_extended_fields_v1';
    const existingMigration1 = await db.queryOne(
      'SELECT * FROM migrations WHERE migration_name = ?',
      [migration1]
    );

    if (!existingMigration1) {
      // Add new columns to users table
      const alterQueries = [
        'ALTER TABLE users ADD COLUMN IF NOT EXISTS is_banned BOOLEAN DEFAULT false',
        'ALTER TABLE users ADD COLUMN IF NOT EXISTS ban_reason TEXT NULL',
        'ALTER TABLE users ADD COLUMN IF NOT EXISTS ban_expires_at TIMESTAMP NULL',
        'ALTER TABLE users ADD COLUMN IF NOT EXISTS banned_by INT NULL',
        'ALTER TABLE users ADD COLUMN IF NOT EXISTS banned_at TIMESTAMP NULL',
        'ALTER TABLE users ADD COLUMN IF NOT EXISTS vip_level ENUM("none", "v1", "v2", "v3", "v4") DEFAULT "none"',
        'ALTER TABLE users ADD COLUMN IF NOT EXISTS vip_expires_at TIMESTAMP NULL',
        'ALTER TABLE users ADD COLUMN IF NOT EXISTS registration_source ENUM("web", "mobile", "api", "admin") DEFAULT "web"',
        'ALTER TABLE users ADD COLUMN IF NOT EXISTS phone VARCHAR(20) NULL',
        'ALTER TABLE users ADD COLUMN IF NOT EXISTS gender ENUM("male", "female", "other") NULL',
        'ALTER TABLE users ADD COLUMN IF NOT EXISTS birth_date DATE NULL',
        'ALTER TABLE users ADD COLUMN IF NOT EXISTS login_count INT DEFAULT 0'
      ];

      for (const query of alterQueries) {
        try {
          await db.query(query);
        } catch (error: any) {
          // Ignore duplicate column errors
          if (!error.message.includes('Duplicate column name')) {
            throw error;
          }
        }
      }

      // Add foreign key constraint if it doesn't exist
      try {
        await db.query(`
          ALTER TABLE users
          ADD CONSTRAINT fk_users_banned_by
          FOREIGN KEY (banned_by) REFERENCES users(id) ON DELETE SET NULL
        `);
      } catch (error: any) {
        // Ignore if constraint already exists
        if (!error.message.includes('Duplicate foreign key constraint')) {
          console.warn('Could not add foreign key constraint:', error.message);
        }
      }

      // Record migration
      await db.query(
        'INSERT INTO migrations (migration_name) VALUES (?)',
        [migration1]
      );

      console.log('Migration completed:', migration1);
    }

    console.log('All migrations completed successfully');
  } catch (error) {
    console.error('Migration error:', error);
    throw error;
  }
}
