import { NextRequest } from 'next/server';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SessionManager, JWTUtils, User } from '@/lib/auth';
import { db } from '@/lib/database';
import {
  successResponse,
  errorResponse,
  validateRequest,
  validators,
  logAuditEvent,
  getClientIP,
  rateLimit
} from '@/lib/api-utils';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting - 开发环境放宽限制
    const clientIP = getClientIP(request);
    const isDevelopment = process.env.NODE_ENV === 'development';
    const maxAttempts = isDevelopment ? 100 : 5; // 开发环境100次，生产环境5次
    const windowMs = isDevelopment ? 60000 : 300000; // 开发环境1分钟，生产环境5分钟

    if (!rateLimit(`login:${clientIP}`, maxAttempts, windowMs)) {
      return errorResponse('登录尝试次数过多，请稍后再试', 429);
    }

    // 安全地解析请求体
    let body;
    try {
      const text = await request.text();

      if (!text || text.trim() === '') {
        return errorResponse('请求体不能为空', 400, 'EMPTY_BODY');
      }
      body = JSON.parse(text);
    } catch (error) {
      console.error('JSON解析错误:', error);
      return errorResponse('请求格式错误，请发送有效的JSON数据', 400, 'INVALID_JSON');
    }

    // Validate request
    const { isValid, data } = validateRequest<{
      emailOrUsername: string;
      password: string;
    }>(body, {
      emailOrUsername: validators.required,
      password: validators.required,
    });

    if (!isValid) {
      return errorResponse('验证失败', 400, 'VALIDATION_ERROR');
    }

    // 首先检查用户是否存在
    const existingUser = await db.queryOne<User>(
      'SELECT * FROM users WHERE (email = ? OR username = ?)',
      [data.emailOrUsername, data.emailOrUsername]
    );

    if (!existingUser) {
      console.log(`用户不存在: ${data.emailOrUsername}`);
      await logAuditEvent({
        action: 'LOGIN_FAILED',
        details: {
          emailOrUsername: data.emailOrUsername,
          reason: 'User not found',
        },
        ipAddress: clientIP,
        userAgent: request.headers.get('user-agent') || undefined,
      });

      return errorResponse('用户不存在，请检查用户名或邮箱', 401, 'USER_NOT_FOUND');
    }

    if (!existingUser.is_active) {
      console.log(`用户已被禁用: ${data.emailOrUsername}`);
      return errorResponse('账户已被禁用，请联系管理员', 401, 'ACCOUNT_DISABLED');
    }

    // Authenticate user
    const user = await UserManager.authenticateUser(
      data.emailOrUsername,
      data.password
    );

    if (!user) {
      console.log(`密码错误: ${data.emailOrUsername}`);
      // Log failed login attempt
      await logAuditEvent({
        action: 'LOGIN_FAILED',
        details: {
          emailOrUsername: data.emailOrUsername,
          reason: 'Invalid password',
        },
        ipAddress: clientIP,
        userAgent: request.headers.get('user-agent') || undefined,
      });

      return errorResponse('密码错误，请重试', 401, 'INVALID_PASSWORD');
    }

    // Create session
    const sessionId = await SessionManager.createSession(user.id);

    // Generate JWT token
    const token = JWTUtils.sign({
      userId: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      sessionId,
    });

    // Log successful login
    await logAuditEvent({
      userId: user.id,
      action: 'LOGIN_SUCCESS',
      details: {
        sessionId,
      },
      ipAddress: clientIP,
      userAgent: request.headers.get('user-agent') || undefined,
    });

    return successResponse({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        avatar_url: user.avatar_url,
        last_login: user.last_login,
      },
      token,
      sessionId,
    }, '登录成功');

  } catch (error) {
    console.error('Login error:', error);
    return errorResponse('服务器内部错误', 500);
  }
}
