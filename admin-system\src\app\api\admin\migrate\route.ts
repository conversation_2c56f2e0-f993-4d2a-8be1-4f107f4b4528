import { NextRequest } from 'next/server';
import { runMigrations } from '@/lib/database';
import { 
  successResponse, 
  errorResponse, 
  withAdminAccess
} from '@/lib/api-utils';

// POST /api/admin/migrate - Run database migrations (admin only)
export async function POST(request: NextRequest) {
  return withAdminAccess()(request, async (req, user, session, clientInfo) => {
    try {
      await runMigrations();
      return successResponse(null, '数据库迁移完成');
    } catch (error) {
      console.error('Migration error:', error);
      return errorResponse('数据库迁移失败', 500);
    }
  });
}
