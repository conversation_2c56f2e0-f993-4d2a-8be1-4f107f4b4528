const mysql = require('mysql2/promise');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'admin_system'
};

async function migrateDatabase() {
  let connection;

  try {
    console.log('🔄 Connecting to MySQL database...');
    connection = await mysql.createConnection(dbConfig);

    console.log('🔄 Running database migrations...');

    // Create migrations table if it doesn't exist
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS migrations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        migration_name VARCHAR(255) UNIQUE NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    `);

    // Migration 1: Add extended user fields
    const migration1 = 'add_user_extended_fields_v1';
    const [existingMigration1] = await connection.execute(
      'SELECT * FROM migrations WHERE migration_name = ?',
      [migration1]
    );

    if (existingMigration1.length === 0) {
      console.log('🔄 Running migration: Add extended user fields...');

      // Add new columns to users table
      const alterQueries = [
        'ALTER TABLE users ADD COLUMN is_banned BOOLEAN DEFAULT false',
        'ALTER TABLE users ADD COLUMN ban_reason TEXT NULL',
        'ALTER TABLE users ADD COLUMN ban_expires_at TIMESTAMP NULL',
        'ALTER TABLE users ADD COLUMN banned_by INT NULL',
        'ALTER TABLE users ADD COLUMN banned_at TIMESTAMP NULL',
        'ALTER TABLE users ADD COLUMN vip_level ENUM("none", "bronze", "silver", "gold", "platinum", "diamond") DEFAULT "none"',
        'ALTER TABLE users ADD COLUMN vip_expires_at TIMESTAMP NULL',
        'ALTER TABLE users ADD COLUMN registration_source ENUM("web", "mobile", "api", "admin") DEFAULT "web"',
        'ALTER TABLE users ADD COLUMN phone VARCHAR(20) NULL',
        'ALTER TABLE users ADD COLUMN gender ENUM("male", "female", "other") NULL',
        'ALTER TABLE users ADD COLUMN birth_date DATE NULL',
        'ALTER TABLE users ADD COLUMN login_count INT DEFAULT 0'
      ];

      for (const query of alterQueries) {
        try {
          await connection.execute(query);
          console.log(`  ✅ ${query}`);
        } catch (error) {
          // Ignore duplicate column errors
          if (error.code === 'ER_DUP_FIELDNAME') {
            console.log(`  ⚠️  Column already exists: ${query}`);
          } else {
            throw error;
          }
        }
      }

      // Add indexes
      const indexQueries = [
        'ALTER TABLE users ADD INDEX idx_is_banned (is_banned)',
        'ALTER TABLE users ADD INDEX idx_vip_level (vip_level)',
        'ALTER TABLE users ADD INDEX idx_registration_source (registration_source)'
      ];

      for (const query of indexQueries) {
        try {
          await connection.execute(query);
          console.log(`  ✅ ${query}`);
        } catch (error) {
          // Ignore duplicate index errors
          if (error.code === 'ER_DUP_KEYNAME') {
            console.log(`  ⚠️  Index already exists: ${query}`);
          } else {
            console.warn(`  ⚠️  Could not add index: ${error.message}`);
          }
        }
      }

      // Add foreign key constraint
      try {
        await connection.execute(`
          ALTER TABLE users
          ADD CONSTRAINT fk_users_banned_by
          FOREIGN KEY (banned_by) REFERENCES users(id) ON DELETE SET NULL
        `);
        console.log('  ✅ Added foreign key constraint for banned_by');
      } catch (error) {
        // Ignore if constraint already exists
        if (error.code === 'ER_DUP_KEYNAME') {
          console.log('  ⚠️  Foreign key constraint already exists');
        } else {
          console.warn(`  ⚠️  Could not add foreign key constraint: ${error.message}`);
        }
      }

      // Record migration
      await connection.execute(
        'INSERT INTO migrations (migration_name) VALUES (?)',
        [migration1]
      );

      console.log('✅ Migration completed:', migration1);
    } else {
      console.log('⚠️  Migration already applied:', migration1);
    }

    console.log('🎉 All migrations completed successfully');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Error details:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateDatabase().catch(error => {
    console.error('Migration script failed:', error);
    process.exit(1);
  });
}

module.exports = { migrateDatabase };
