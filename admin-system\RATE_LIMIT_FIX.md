# 频率限制修复说明

## 问题描述
开发环境下登录API触发429错误，即使配置了`RATE_LIMIT_MAX_REQUESTS=100`和`RATE_LIMIT_WINDOW_MS=60000`，但实际上只允许几次登录尝试就被限制。

## 问题原因
1. **登录API限制过严**：`/api/auth/login` 硬编码了5次/5分钟的限制
2. **移动端API限制**：使用客户端检测，可能被识别为UNKNOWN客户端，限制为50次/小时
3. **多层限制叠加**：不同层级的限制相互影响

## 修复内容

### 1. 修复普通登录API (`/api/auth/login/route.ts`)
```typescript
// 修改前：硬编码5次/5分钟
if (!rateLimit(`login:${clientIP}`, 5, 300000)) {

// 修改后：开发环境放宽限制
const isDevelopment = process.env.NODE_ENV === 'development';
const maxAttempts = isDevelopment ? 100 : 5; // 开发环境100次，生产环境5次
const windowMs = isDevelopment ? 60000 : 300000; // 开发环境1分钟，生产环境5分钟

if (!rateLimit(`login:${clientIP}`, maxAttempts, windowMs)) {
```

### 2. 修复移动端登录API (`/api/mobile/auth/login/route.ts`)
```typescript
// 修改前：使用客户端配置的限制
const rateLimitConfig = ClientPermissionChecker.getRateLimit(clientInfo.type);
if (!rateLimit(`mobile-login:${clientIP}`, rateLimitConfig.requests, rateLimitConfig.window)) {

// 修改后：开发环境放宽限制
const isDevelopment = process.env.NODE_ENV === 'development';
const maxRequests = isDevelopment ? 1000 : rateLimitConfig.requests;
const windowMs = isDevelopment ? 60000 : rateLimitConfig.window;

if (!rateLimit(`mobile-login:${clientIP}`, maxRequests, windowMs)) {
```

### 3. 修复客户端权限检查器 (`/lib/client-detection.ts`)
```typescript
// 修改前：直接返回配置的限制
static getRateLimit(clientType: ClientType): { requests: number; window: number } {
  const permissions = CLIENT_PERMISSIONS[clientType];
  return permissions?.rateLimit || CLIENT_PERMISSIONS[ClientType.UNKNOWN].rateLimit;
}

// 修改后：开发环境下放宽限制
static getRateLimit(clientType: ClientType): { requests: number; window: number } {
  const permissions = CLIENT_PERMISSIONS[clientType];
  const rateLimit = permissions?.rateLimit || CLIENT_PERMISSIONS[ClientType.UNKNOWN].rateLimit;
  
  // 开发环境下放宽限制
  if (process.env.NODE_ENV === 'development') {
    return {
      requests: Math.max(rateLimit.requests * 10, 1000), // 至少1000次
      window: Math.min(rateLimit.window, 60000), // 最多1分钟窗口
    };
  }
  
  return rateLimit;
}
```

## 修复效果
- **开发环境**：登录API允许100-1000次请求/分钟，大大提高开发效率
- **生产环境**：保持原有的安全限制不变
- **环境隔离**：通过`NODE_ENV`环境变量自动切换限制策略

## 验证方法
1. 重启开发服务器
2. 尝试多次登录，应该不再出现429错误
3. 检查控制台日志，确认使用了开发环境的限制配置

## 注意事项
- 生产环境的安全限制保持不变
- 如需调整生产环境限制，请修改对应的硬编码值
- 建议在生产环境使用Redis等外部存储来管理频率限制状态
