# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=admin_system

# JWT Secret - Change this in production!
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key

# API Configuration
API_BASE_URL=http://localhost:3000/api

# CORS Configuration
# 允许的跨域源地址，用逗号分隔
CORS_ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000,http://localhost:3333
# 生产环境域名（不包含协议）
PRODUCTION_DOMAIN=your-domain.com

# Rate Limiting Configuration
# 频率限制：每个时间窗口内允许的最大请求数
RATE_LIMIT_MAX_REQUESTS=100
# 频率限制：时间窗口大小（毫秒）
RATE_LIMIT_WINDOW_MS=60000

# Environment
NODE_ENV=development

# Server Port
PORT=3333
