/**
 * Admin System API 客户端
 * 提供类型安全的API调用接口
 */

import { ApiResponse } from './api-utils';

// API 配置
const API_CONFIG = {
  baseURL: process.env.NEXT_PUBLIC_API_URL || '/api',
  timeout: 10000,
  retryCount: 3,
  retryDelay: 1000,
};

// 请求配置接口
interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retryCount?: number;
  showError?: boolean;
}

// 错误类型
export class ApiClientError extends Error {
  constructor(
    public statusCode: number,
    message: string,
    public code?: string,
    public data?: any
  ) {
    super(message);
    this.name = 'ApiClientError';
  }
}

// Token 管理
class TokenManager {
  private static readonly TOKEN_KEY = 'auth_token';
  private static readonly USER_KEY = 'auth_user';

  static getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.TOKEN_KEY);
  }

  static setToken(token: string): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  static clearToken(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.USER_KEY);
  }

  static getUser(): any {
    if (typeof window === 'undefined') return null;
    const userStr = localStorage.getItem(this.USER_KEY);
    return userStr ? JSON.parse(userStr) : null;
  }

  static setUser(user: any): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.USER_KEY, JSON.stringify(user));
  }
}

// 请求拦截器
function requestInterceptor(url: string, config: RequestConfig): RequestConfig {
  const token = TokenManager.getToken();

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...config.headers,
  };

  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }

  return {
    ...config,
    headers,
  };
}

// 响应拦截器
async function responseInterceptor<T>(response: Response): Promise<ApiResponse<T>> {
  const contentType = response.headers.get('content-type');
  
  let data: any;
  if (contentType && contentType.includes('application/json')) {
    data = await response.json();
  } else {
    data = await response.text();
  }

  if (!response.ok) {
    // HTTP 错误
    if (response.status === 401) {
      // 未授权，清除token
      TokenManager.clearToken();
      
      // 如果在浏览器环境且不在登录页，跳转到登录页
      if (typeof window !== 'undefined' && !window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }

    throw new ApiClientError(
      response.status,
      data?.error || data?.message || `HTTP ${response.status}`,
      data?.code,
      data
    );
  }

  // 业务逻辑错误检查
  if (data && typeof data === 'object' && data.success === false) {
    throw new ApiClientError(
      response.status,
      data.error || data.message || '请求失败',
      data.code,
      data
    );
  }

  return data;
}

// 重试机制
async function withRetry<T>(
  requestFn: () => Promise<T>,
  retryCount: number = API_CONFIG.retryCount
): Promise<T> {
  let lastError: Error;

  for (let i = 0; i <= retryCount; i++) {
    try {
      return await requestFn();
    } catch (error) {
      lastError = error as Error;
      
      // 如果是客户端错误（4xx）或最后一次重试，直接抛出错误
      if (error instanceof ApiClientError && error.statusCode < 500) {
        throw error;
      }
      
      if (i === retryCount) {
        throw error;
      }

      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, API_CONFIG.retryDelay * (i + 1)));
    }
  }

  throw lastError!;
}

// 核心请求函数
async function request<T = any>(
  endpoint: string,
  config: RequestConfig = {}
): Promise<ApiResponse<T>> {
  const url = `${API_CONFIG.baseURL}${endpoint}`;
  const interceptedConfig = requestInterceptor(url, config);

  const requestFn = async () => {
    const controller = new AbortController();
    const timeoutId = setTimeout(
      () => controller.abort(),
      config.timeout || API_CONFIG.timeout
    );

    try {
      const response = await fetch(url, {
        method: config.method || 'GET',
        headers: interceptedConfig.headers,
        body: config.body ? JSON.stringify(config.body) : undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return await responseInterceptor<T>(response);
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof DOMException && error.name === 'AbortError') {
        throw new ApiClientError(408, '请求超时', 'TIMEOUT');
      }
      
      throw error;
    }
  };

  return withRetry(requestFn, config.retryCount);
}

// API 客户端类
export class ApiClient {
  // GET 请求
  static async get<T = any>(
    endpoint: string,
    params?: Record<string, any>,
    config?: Omit<RequestConfig, 'method' | 'body'>
  ): Promise<ApiResponse<T>> {
    let url = endpoint;
    
    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
      
      const queryString = searchParams.toString();
      if (queryString) {
        url += `?${queryString}`;
      }
    }

    return request<T>(url, { ...config, method: 'GET' });
  }

  // POST 请求
  static async post<T = any>(
    endpoint: string,
    data?: any,
    config?: Omit<RequestConfig, 'method'>
  ): Promise<ApiResponse<T>> {
    return request<T>(endpoint, { ...config, method: 'POST', body: data });
  }

  // PUT 请求
  static async put<T = any>(
    endpoint: string,
    data?: any,
    config?: Omit<RequestConfig, 'method'>
  ): Promise<ApiResponse<T>> {
    return request<T>(endpoint, { ...config, method: 'PUT', body: data });
  }

  // DELETE 请求
  static async delete<T = any>(
    endpoint: string,
    config?: Omit<RequestConfig, 'method' | 'body'>
  ): Promise<ApiResponse<T>> {
    return request<T>(endpoint, { ...config, method: 'DELETE' });
  }

  // PATCH 请求
  static async patch<T = any>(
    endpoint: string,
    data?: any,
    config?: Omit<RequestConfig, 'method'>
  ): Promise<ApiResponse<T>> {
    return request<T>(endpoint, { ...config, method: 'PATCH', body: data });
  }

  // Token 管理方法
  static getToken = TokenManager.getToken;
  static setToken = TokenManager.setToken;
  static clearToken = TokenManager.clearToken;
  static getUser = TokenManager.getUser;
  static setUser = TokenManager.setUser;

  // 配置方法
  static setBaseURL(url: string): void {
    API_CONFIG.baseURL = url;
  }

  static setTimeout(timeout: number): void {
    API_CONFIG.timeout = timeout;
  }

  static setRetryConfig(count: number, delay: number): void {
    API_CONFIG.retryCount = count;
    API_CONFIG.retryDelay = delay;
  }
}

export default ApiClient;
