/**
 * UniApp API 封装工具
 * 提供统一的HTTP请求封装、错误处理、响应拦截等功能
 */

// API 配置
const API_CONFIG = {
  // 开发环境API地址
  DEV_BASE_URL: 'http://localhost:3333/api',
  // 生产环境API地址
  PROD_BASE_URL: 'https://your-domain.com/api',
  // 请求超时时间
  TIMEOUT: 10000,
  // 重试次数
  RETRY_COUNT: 3,
  // 重试延迟时间(ms)
  RETRY_DELAY: 1000
};

// 获取当前环境的API基础URL
function getBaseUrl() {
  // #ifdef H5
  return process.env.NODE_ENV === 'development' ? API_CONFIG.DEV_BASE_URL : API_CONFIG.PROD_BASE_URL;
  // #endif
  
  // #ifndef H5
  return API_CONFIG.PROD_BASE_URL;
  // #endif
}

// 响应状态码映射
const STATUS_CODE_MAP = {
  200: '请求成功',
  400: '请求参数错误',
  401: '未授权，请重新登录',
  403: '权限不足',
  404: '请求的资源不存在',
  405: '请求方法不允许',
  408: '请求超时',
  409: '数据冲突',
  422: '数据验证失败',
  429: '请求过于频繁，请稍后再试',
  500: '服务器内部错误',
  502: '网关错误',
  503: '服务不可用',
  504: '网关超时'
};

// 获取错误消息
function getErrorMessage(statusCode, defaultMessage = '网络请求失败') {
  return STATUS_CODE_MAP[statusCode] || defaultMessage;
}

// Token管理
const TokenManager = {
  // 获取token
  getToken() {
    try {
      return uni.getStorageSync('access_token') || '';
    } catch (e) {
      console.error('获取token失败:', e);
      return '';
    }
  },

  // 设置token
  setToken(token) {
    try {
      uni.setStorageSync('access_token', token);
    } catch (e) {
      console.error('设置token失败:', e);
    }
  },

  // 清除token
  clearToken() {
    try {
      uni.removeStorageSync('access_token');
    } catch (e) {
      console.error('清除token失败:', e);
    }
  }
};

// 统一提示管理
const ToastManager = {
  // 成功提示
  success(message, duration = 2000) {
    uni.showToast({
      title: message,
      icon: 'success',
      duration: duration,
      mask: true
    });
  },

  // 错误提示
  error(message, duration = 3000) {
    uni.showToast({
      title: message,
      icon: 'none',
      duration: duration,
      mask: true
    });
  },

  // 加载提示
  loading(message = '加载中...') {
    uni.showLoading({
      title: message,
      mask: true
    });
  },

  // 隐藏加载
  hideLoading() {
    uni.hideLoading();
  },

  // 确认对话框
  confirm(options = {}) {
    const defaultOptions = {
      title: '提示',
      content: '确定要执行此操作吗？',
      confirmText: '确定',
      cancelText: '取消'
    };
    
    return new Promise((resolve) => {
      uni.showModal({
        ...defaultOptions,
        ...options,
        success: (res) => {
          resolve(res.confirm);
        },
        fail: () => {
          resolve(false);
        }
      });
    });
  }
};

// 请求拦截器
function requestInterceptor(config) {
  // 添加token
  const token = TokenManager.getToken();
  if (token) {
    config.header = config.header || {};
    config.header.Authorization = `Bearer ${token}`;
  }

  // 添加通用header
  config.header = {
    'Content-Type': 'application/json',
    ...config.header
  };

  // 添加基础URL
  if (!config.url.startsWith('http')) {
    config.url = getBaseUrl() + config.url;
  }

  // 设置超时时间
  config.timeout = config.timeout || API_CONFIG.TIMEOUT;

  console.log('请求拦截器:', config);
  return config;
}

// 响应拦截器
function responseInterceptor(response, config) {
  console.log('响应拦截器:', response);

  const { statusCode, data } = response;

  // HTTP状态码检查
  if (statusCode >= 200 && statusCode < 300) {
    // 业务逻辑检查
    if (data && typeof data === 'object') {
      if (data.success === false) {
        // 业务错误
        const errorMessage = data.error || data.message || '操作失败';
        
        // 特殊错误码处理
        if (data.code === 'INVALID_CREDENTIALS' || statusCode === 401) {
          // 未授权，清除token并跳转登录
          TokenManager.clearToken();
          ToastManager.error(errorMessage); // 使用后端返回的错误消息

          // 延迟跳转，避免多次跳转
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/login/login'
            });
          }, 1500);

          return Promise.reject({
            code: 'UNAUTHORIZED',
            message: errorMessage,
            statusCode
          });
        }
        
        return Promise.reject({
          code: data.code || 'BUSINESS_ERROR',
          message: errorMessage,
          statusCode
        });
      }
      
      // 成功响应
      return data;
    }
    
    // 非JSON响应
    return response;
  } else {
    // HTTP错误 - 但先检查响应体中是否有具体的错误信息
    let errorMessage = getErrorMessage(statusCode);
    let errorCode = 'HTTP_ERROR';

    // 如果响应体包含JSON格式的错误信息，优先使用后端返回的错误消息
    if (data && typeof data === 'object' && (data.error || data.message)) {
      errorMessage = data.error || data.message || errorMessage;
      errorCode = data.code || 'HTTP_ERROR';
    }

    // 401未授权特殊处理
    if (statusCode === 401) {
      TokenManager.clearToken();
      ToastManager.error(errorMessage); // 使用后端返回的错误消息或默认消息
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login'
        });
      }, 1500);
    }

    return Promise.reject({
      code: errorCode,
      message: errorMessage,
      statusCode
    });
  }
}

// 错误处理器
function errorHandler(error, config) {
  console.error('请求错误:', error);

  let errorMessage = '网络请求失败';
  
  if (error.errMsg) {
    // uni.request的错误
    if (error.errMsg.includes('timeout')) {
      errorMessage = '请求超时，请检查网络连接';
    } else if (error.errMsg.includes('fail')) {
      errorMessage = '网络连接失败，请检查网络设置';
    }
  } else if (error.message) {
    // 自定义错误
    errorMessage = error.message;
  }

  // 显示错误提示（可配置是否显示）
  if (config.showError !== false) {
    ToastManager.error(errorMessage);
  }

  return Promise.reject({
    code: error.code || 'NETWORK_ERROR',
    message: errorMessage,
    statusCode: error.statusCode || 0,
    originalError: error
  });
}

// 重试机制
async function retryRequest(requestFn, retryCount = API_CONFIG.RETRY_COUNT) {
  for (let i = 0; i < retryCount; i++) {
    try {
      return await requestFn();
    } catch (error) {
      console.log(`请求失败，第${i + 1}次重试:`, error);
      
      // 最后一次重试失败
      if (i === retryCount - 1) {
        throw error;
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, API_CONFIG.RETRY_DELAY));
    }
  }
}

// 核心请求函数
function request(config = {}) {
  return new Promise((resolve, reject) => {
    // 请求拦截
    const interceptedConfig = requestInterceptor(config);
    
    // 发起请求
    uni.request({
      ...interceptedConfig,
      success: (response) => {
        try {
          // 响应拦截
          const result = responseInterceptor(response, interceptedConfig);
          resolve(result);
        } catch (error) {
          // 响应拦截器抛出的错误
          const handledError = errorHandler(error, interceptedConfig);
          reject(handledError);
        }
      },
      fail: (error) => {
        // 网络错误
        const handledError = errorHandler(error, interceptedConfig);
        reject(handledError);
      }
    });
  });
}

// 导出API对象
const api = {
  // 基础请求方法
  request,
  
  // GET请求
  get(url, params = {}, config = {}) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    
    return request({
      url: fullUrl,
      method: 'GET',
      ...config
    });
  },

  // POST请求
  post(url, data = {}, config = {}) {
    return request({
      url,
      method: 'POST',
      data,
      ...config
    });
  },

  // PUT请求
  put(url, data = {}, config = {}) {
    return request({
      url,
      method: 'PUT',
      data,
      ...config
    });
  },

  // DELETE请求
  delete(url, config = {}) {
    return request({
      url,
      method: 'DELETE',
      ...config
    });
  },

  // 工具方法
  TokenManager,
  ToastManager,
  
  // 配置
  config: API_CONFIG,
  
  // 设置基础URL
  setBaseUrl(url) {
    API_CONFIG.PROD_BASE_URL = url;
  }
};

export default api;
