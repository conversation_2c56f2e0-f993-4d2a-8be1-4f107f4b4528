<template>
	<view class="profile-container">
		<!-- 主要内容区域 -->
		<view class="main-content">
			<!-- 页面标题 -->
			<view class="page-header">
				<text class="page-title">设置</text>
			</view>

			<!-- 账户信息 -->
			<view class="account-section">
				<text class="section-title">账户</text>
				<view class="account-card" @click="goToUserProfile">
					<view class="account-info">
						<image class="user-avatar" :src="getUserAvatar()" mode="aspectFit"></image>
						<view class="user-details">
							<text class="user-name">{{ getUserDisplayName() }}</text>
							<text class="user-email">{{ getUserEmail() }}</text>
						</view>
					</view>
					<image class="arrow-icon" src="/static/figma-assets/assets/343f0e56-7402-48b6-b725-9d243ae7e65d.png" mode="aspectFit"></image>
				</view>
			</view>

			<!-- 设置选项 -->
			<view class="settings-section">
			<text class="section-title">设置</text>
			<view class="settings-card">
				<!-- 语言设置 -->
				<view class="setting-item" @click="goToLanguage">
					<view class="setting-left">
						<view class="setting-icon-bg">
							<image class="setting-icon" src="/static/figma-assets/assets/bd1b79e4-4715-4004-a7b3-e2e389d920bb.png" mode="aspectFit"></image>
						</view>
						<text class="setting-text">语言</text>
						<text class="setting-value">中文</text>
					</view>
					<image class="arrow-icon" src="/static/figma-assets/assets/343f0e56-7402-48b6-b725-9d243ae7e65d.png" mode="aspectFit"></image>
				</view>

				<!-- 通知设置 -->
				<view class="setting-item">
					<view class="setting-left">
						<view class="setting-icon-bg">
							<image class="setting-icon" src="/static/figma-assets/assets/1072999d-28ae-4367-9d21-6ecebdd537dc.png" mode="aspectFit"></image>
						</view>
						<text class="setting-text">通知</text>
					</view>
					<view class="toggle-switch" :class="{ active: notificationEnabled }" @click="toggleNotification">
						<view class="toggle-slider"></view>
					</view>
				</view>

				<!-- 夜间模式 -->
				<view class="setting-item">
					<view class="setting-left">
						<view class="setting-icon-bg">
							<image class="setting-icon" src="/static/figma-assets/assets/fdd91889-f928-4ede-9944-bbbfbc51cddc.png" mode="aspectFit"></image>
						</view>
						<text class="setting-text">夜间模式</text>
					</view>
					<view class="toggle-switch" :class="{ active: darkModeEnabled }" @click="toggleDarkMode">
						<view class="toggle-slider"></view>
					</view>
				</view>
			</view>
		</view>

			<!-- 退出登录按钮 -->
			<view class="logout-button" @click="logout">
				<text class="logout-text">退出登录</text>
			</view>
		</view>

		<!-- 底部导航栏 -->
		<view class="tabbar-container">
			<TabBar currentPage="profile" />
		</view>
	</view>
</template>

<script>
import TabBar from '@/components/TabBar.vue'
import { AuthHelper } from '@/utils/auth-helper.js'
import { pageLifecycleMixin } from '@/utils/app-init.js'

export default {
	components: {
		TabBar
	},
	mixins: [pageLifecycleMixin],
	data() {
		return {
			notificationEnabled: true,
			darkModeEnabled: true,
			userInfo: null,
			loading: true,
			requireAuth: true // 标记此页面需要登录
		}
	},
	async onLoad() {
		await this.checkAuthAndLoadData();
	},
	onShow() {
		// 每次显示页面时检查登录状态
		if (!AuthHelper.isLoggedIn()) {
			AuthHelper.redirectToLogin();
			return;
		}
		// 刷新用户信息
		this.loadUserInfo();
	},
	methods: {
		// 检查登录状态并加载数据
		async checkAuthAndLoadData() {
			if (!AuthHelper.isLoggedIn()) {
				AuthHelper.redirectToLogin();
				return;
			}

			await this.loadUserInfo();
		},

		// 加载用户信息
		async loadUserInfo() {
			try {
				this.loading = true;
				const userInfo = AuthHelper.getCurrentUser();

				if (userInfo) {
					this.userInfo = userInfo;
				} else {
					// 如果本地没有用户信息，尝试从服务器获取
					const result = await AuthHelper.refreshUserInfo();
					if (result.success) {
						this.userInfo = result.user;
					} else {
						// 获取失败，可能token已过期
						AuthHelper.redirectToLogin();
						return;
					}
				}
			} catch (error) {
				console.error('加载用户信息失败:', error);
				AuthHelper.redirectToLogin();
			} finally {
				this.loading = false;
			}
		},

		goToUserProfile() {
			if (!AuthHelper.isLoggedIn()) {
				AuthHelper.redirectToLogin();
				return;
			}

			uni.navigateTo({
				url: '/pages/user-profile/user-profile'
			});
		},
		goToLanguage() {
			console.log('前往语言设置');
			// 这里可以添加跳转到语言设置页面的逻辑
		},
		toggleNotification() {
			this.notificationEnabled = !this.notificationEnabled;
			console.log('通知设置:', this.notificationEnabled ? '开启' : '关闭');
			// 这里可以添加保存通知设置的逻辑
		},
		toggleDarkMode() {
			this.darkModeEnabled = !this.darkModeEnabled;
			console.log('夜间模式:', this.darkModeEnabled ? '开启' : '关闭');
			// 这里可以添加保存夜间模式设置的逻辑
		},
		async logout() {
			try {
				const confirmed = await uni.showModal({
					title: '提示',
					content: '确定要退出登录吗？',
					showCancel: true,
					confirmText: '退出',
					cancelText: '取消'
				});

				if (confirmed.confirm) {
					// 执行登出
					const result = await AuthHelper.logout();
					if (result.success) {
						// 登出成功，跳转到登录页
						uni.reLaunch({
							url: '/pages/login/login'
						});
					}
				}
			} catch (error) {
				console.error('退出登录失败:', error);
				uni.showToast({
					title: '操作失败，请重试',
					icon: 'none'
				});
			}
		},

		// 获取用户显示名称
		getUserDisplayName() {
			if (!this.userInfo) return '未登录';
			return this.userInfo.full_name || this.userInfo.username || '用户';
		},

		// 获取用户邮箱
		getUserEmail() {
			if (!this.userInfo) return '';
			return this.userInfo.email || '';
		},

		// 获取用户头像
		getUserAvatar() {
			if (!this.userInfo || !this.userInfo.avatar_url) {
				return '/static/figma-assets/assets/b0b4af09-8098-4d18-aed3-71537580e1af.png';
			}
			return this.userInfo.avatar_url;
		}
	}
}
</script>

<style scoped>
.profile-container {
	width: 100%;
	height: 100vh;
	background-color: #222222;
	display: flex;
	flex-direction: column;
	overflow-x: hidden;
}

/* 主要内容区域 */
.main-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow-y: auto;
}

/* 页面标题 */
.page-header {
	width: 100%;
	display: flex;
	justify-content: center;
	margin-top: 100rpx;
	/* 确保在微信小程序中不与状态栏和胶囊按钮重叠 */
	margin-top: calc(100rpx + env(safe-area-inset-top));
	/* 为微信小程序添加额外的顶部间距 */
	/* #ifdef MP-WEIXIN */
	margin-top: calc(140rpx + env(safe-area-inset-top));
	/* #endif */
}

.page-title {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 36rpx;
	font-weight: bold;
	color: #FFFFFF;
}

/* 账户信息 */
.account-section {
	width: 720rpx;
	margin: 28rpx auto 0;
}

.section-title {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 30rpx;
	font-weight: bold;
	color: #FFFFFF;
	letter-spacing: 0.16rpx;
	display: block;
	margin-bottom: 24rpx;
}

.account-card {
	width: 100%;
	height: 148rpx;
	background-color: rgba(255, 255, 255, 0.29);
	border-radius: 26rpx;
	padding: 30rpx;
	box-sizing: border-box;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.account-info {
	display: flex;
	align-items: center;
}

.user-avatar {
	width: 84rpx;
	height: 84rpx;
	border-radius: 20rpx;
}

.user-details {
	margin-left: 26rpx;
	display: flex;
	flex-direction: column;
}

.user-name {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 28rpx;
	font-weight: 500;
	color: #FFFFFF;
	letter-spacing: 0.14rpx;
	line-height: 40rpx;
}

.user-email {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 20rpx;
	font-weight: 500;
	color: #FFFFFF;
	letter-spacing: 0.14rpx;
}

.arrow-icon {
	width: 84rpx;
	height: 84rpx;
	border-radius: 20rpx;
}

/* 设置选项 */
.settings-section {
	width: 720rpx;
	margin: 42rpx auto 0;
}

.settings-card {
	width: 100%;
	background-color: rgba(255, 255, 255, 0.29);
	border-radius: 30rpx;
	padding: 30rpx;
	box-sizing: border-box;
	margin-top: 24rpx;
}

.setting-item {
	width: 100%;
	height: 84rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 36rpx;
}

.setting-item:last-child {
	margin-bottom: 0;
}

.setting-left {
	display: flex;
	align-items: center;
	flex: 1;
}

.setting-icon-bg {
	width: 84rpx;
	height: 84rpx;
	border-radius: 20rpx;
	background-color: rgba(255, 255, 255, 0.26);
	display: flex;
	justify-content: center;
	align-items: center;
	margin-right: 26rpx;
}

.setting-icon {
	width: 40rpx;
	height: 40rpx;
}

.setting-text {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 30rpx;
	font-weight: 500;
	color: #FFFFFF;
	letter-spacing: 0.16rpx;
}

.setting-value {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 24rpx;
	font-weight: bold;
	color: #FFFFFF;
	letter-spacing: 0.12rpx;
	margin-left: auto;
	margin-right: 26rpx;
}

/* 开关样式 */
.toggle-switch {
	width: 90rpx;
	height: 52rpx;
	border-radius: 32rpx;
	background-color: #F2282D;
	position: relative;
	transition: all 0.3s ease;
}

.toggle-slider {
	width: 34rpx;
	height: 34rpx;
	border-radius: 28rpx;
	background-color: #FFFFFF;
	position: absolute;
	top: 9rpx;
	right: 6rpx;
	transition: all 0.3s ease;
	box-shadow: 0px 2rpx 6rpx 0px rgba(0,0,0,0.1), 0px 2rpx 4rpx 0px rgba(0,0,0,0.1), 0px 4rpx 8rpx 0px rgba(0,0,0,0.1);
}

.toggle-switch:not(.active) {
	background-color: #CCCCCC;
}

.toggle-switch:not(.active) .toggle-slider {
	right: 50rpx;
}

/* 退出登录按钮 */
.logout-button {
	width: 720rpx;
	height: 112rpx;
	margin: 80rpx auto 0;
	background-color: #F2282D;
	border-radius: 200rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.logout-text {
	font-family: 'Samsung Sharp Sans', sans-serif;
	font-size: 32rpx;
	font-weight: bold;
	color: #FFFFFF;
}

/* TabBar容器 */
.tabbar-container {
	background-color: #222222;
	padding: 0 34rpx 34rpx;
	display: flex;
	justify-content: center;
	flex-shrink: 0;
}

/* uniapp使用rpx单位自动适配不同屏幕尺寸，无需媒体查询 */
</style>
