/**
 * 统一错误处理Hook
 * 自动根据错误码和接口返回的错误信息显示提示
 */

import { useCallback } from 'react';
import { toast } from 'sonner';
import { ApiClientError } from '@/lib/api-client';
import { useRouter } from 'next/navigation';

// 错误类型定义
export interface ErrorInfo {
  code?: string;
  message: string;
  statusCode?: number;
  data?: any;
}

// 错误处理配置
interface ErrorHandlerConfig {
  showToast?: boolean;
  redirectOnAuth?: boolean;
  customHandler?: (error: ErrorInfo) => boolean; // 返回true表示已处理，不再执行默认处理
}

// 只保留必要的认证相关错误码，其他错误完全依赖后端返回的消息

// 需要重定向到登录页的错误码
const AUTH_ERROR_CODES = ['UNAUTHORIZED', 'INVALID_CREDENTIALS', 'TOKEN_EXPIRED'];

export function useErrorHandler() {
  const router = useRouter();

  /**
   * 处理错误的核心函数
   */
  const handleError = useCallback((
    error: any,
    config: ErrorHandlerConfig = {}
  ): ErrorInfo => {
    const {
      showToast = true,
      redirectOnAuth = true,
      customHandler
    } = config;

    // 标准化错误信息
    const errorInfo = normalizeError(error);

    // 如果有自定义处理器且返回true，则不执行默认处理
    if (customHandler && customHandler(errorInfo)) {
      return errorInfo;
    }

    // 处理认证错误
    if (shouldRedirectToLogin(errorInfo) && redirectOnAuth) {
      if (showToast) {
        toast.error(errorInfo.message);
      }
      
      // 延迟跳转，确保toast显示
      setTimeout(() => {
        router.push('/login');
      }, 1000);
      
      return errorInfo;
    }

    // 显示错误提示
    if (showToast) {
      const toastType = getToastType(errorInfo);
      
      switch (toastType) {
        case 'warning':
          toast.warning(errorInfo.message);
          break;
        case 'info':
          toast.info(errorInfo.message);
          break;
        case 'error':
        default:
          toast.error(errorInfo.message);
          break;
      }
    }

    return errorInfo;
  }, [router]);

  /**
   * 包装异步函数，自动处理错误
   */
  const withErrorHandling = useCallback(<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    config?: ErrorHandlerConfig
  ) => {
    return async (...args: T): Promise<R | null> => {
      try {
        return await fn(...args);
      } catch (error) {
        handleError(error, config);
        return null;
      }
    };
  }, [handleError]);

  /**
   * 包装API调用，自动处理错误并返回结果
   */
  const safeApiCall = useCallback(async <T>(
    apiCall: () => Promise<T>,
    config?: ErrorHandlerConfig
  ): Promise<{ data: T | null; error: ErrorInfo | null }> => {
    try {
      const data = await apiCall();
      return { data, error: null };
    } catch (error) {
      const errorInfo = handleError(error, config);
      return { data: null, error: errorInfo };
    }
  }, [handleError]);

  return {
    handleError,
    withErrorHandling,
    safeApiCall,
  };
}

/**
 * 标准化错误信息
 * 完全依赖后端返回的错误消息，不进行任何硬编码处理
 */
function normalizeError(error: any): ErrorInfo {
  // ApiClientError - 直接使用后端返回的错误消息
  if (error instanceof ApiClientError) {
    return {
      code: error.code,
      message: error.message, // 直接使用后端返回的消息
      statusCode: error.statusCode,
      data: error.data,
    };
  }

  // 标准Error对象
  if (error instanceof Error) {
    return {
      message: error.message,
    };
  }

  // 自定义错误对象 - 直接使用后端返回的错误消息
  if (error && typeof error === 'object') {
    // 优先使用后端返回的错误消息，不添加任何默认值
    const message = error.message || error.error || error.msg;
    return {
      code: error.code,
      message: message || '操作失败', // 只在完全没有消息时使用最基本的提示
      statusCode: error.statusCode || error.status,
      data: error.data,
    };
  }

  // 字符串错误
  if (typeof error === 'string') {
    return {
      message: error,
    };
  }

  // 其他类型 - 只在完全无法获取错误信息时使用
  return {
    message: '操作失败',
  };
}

/**
 * 判断是否需要重定向到登录页
 */
function shouldRedirectToLogin(errorInfo: ErrorInfo): boolean {
  // 检查状态码
  if (errorInfo.statusCode === 401) {
    return true;
  }

  // 检查错误码
  if (errorInfo.code && AUTH_ERROR_CODES.includes(errorInfo.code)) {
    return true;
  }

  return false;
}

/**
 * 获取Toast类型
 */
function getToastType(errorInfo: ErrorInfo): 'error' | 'warning' | 'info' {
  // 根据状态码判断
  if (errorInfo.statusCode) {
    if (errorInfo.statusCode >= 400 && errorInfo.statusCode < 500) {
      return 'warning'; // 客户端错误
    }
    if (errorInfo.statusCode >= 500) {
      return 'error'; // 服务器错误
    }
  }

  // 根据错误码判断
  if (errorInfo.code) {
    const warningCodes = ['VALIDATION_ERROR', 'CONFLICT', 'NOT_FOUND'];
    if (warningCodes.includes(errorInfo.code)) {
      return 'warning';
    }
  }

  return 'error';
}

/**
 * 获取友好的错误消息
 * 完全依赖后端返回的错误消息，不进行任何硬编码处理
 */
export function getFriendlyErrorMessage(errorInfo: ErrorInfo): string {
  // 直接返回后端提供的错误消息
  // 如果后端没有提供消息，只返回最基本的提示
  return errorInfo.message || '操作失败';
}
