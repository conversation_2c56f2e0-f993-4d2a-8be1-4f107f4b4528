/**
 * API调用辅助工具
 * 提供统一的API调用方式，自动处理错误和加载状态
 */

import { toast } from 'sonner';
import { ApiClientError } from './api-client';
import { ApiResponse } from './api-utils';

// 加载状态管理
interface LoadingState {
  [key: string]: boolean;
}

let globalLoadingState: LoadingState = {};
const loadingListeners: Array<(state: LoadingState) => void> = [];

// 加载状态管理器
export const LoadingManager = {
  setLoading(key: string, loading: boolean) {
    globalLoadingState = { ...globalLoadingState, [key]: loading };
    loadingListeners.forEach(listener => listener(globalLoadingState));
  },

  isLoading(key: string): boolean {
    return globalLoadingState[key] || false;
  },

  subscribe(listener: (state: LoadingState) => void) {
    loadingListeners.push(listener);
    return () => {
      const index = loadingListeners.indexOf(listener);
      if (index > -1) {
        loadingListeners.splice(index, 1);
      }
    };
  },

  getState(): LoadingState {
    return { ...globalLoadingState };
  }
};

// API调用配置
interface ApiCallConfig {
  loadingKey?: string;
  showSuccessToast?: boolean;
  successMessage?: string;
  showErrorToast?: boolean;
  errorMessage?: string;
  redirectOnAuth?: boolean;
  retryCount?: number;
  retryDelay?: number;
}

// API调用结果
interface ApiCallResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

/**
 * 统一的API调用函数
 * 自动处理加载状态、错误处理、成功提示等
 */
export async function apiCall<T>(
  apiFunction: () => Promise<ApiResponse<T>>,
  config: ApiCallConfig = {}
): Promise<ApiCallResult<T>> {
  const {
    loadingKey,
    showSuccessToast = false,
    successMessage,
    showErrorToast = true,
    errorMessage,
    redirectOnAuth = true,
    retryCount = 0,
    retryDelay = 1000
  } = config;

  // 设置加载状态
  if (loadingKey) {
    LoadingManager.setLoading(loadingKey, true);
  }

  let lastError: any;

  // 重试机制
  for (let attempt = 0; attempt <= retryCount; attempt++) {
    try {
      const response = await apiFunction();

      // 清除加载状态
      if (loadingKey) {
        LoadingManager.setLoading(loadingKey, false);
      }

      // 检查响应格式
      if (response && typeof response === 'object') {
        // 标准API响应格式
        if ('success' in response) {
          if (response.success) {
            // 成功响应
            if (showSuccessToast && (successMessage || response.message)) {
              toast.success(successMessage || response.message || '操作成功');
            }
            
            return {
              success: true,
              data: response.data,
            };
          } else {
            // 业务错误 - 直接使用后端返回的错误消息
            const error = response.error || response.message || '操作失败';

            if (showErrorToast) {
              toast.error(errorMessage || error);
            }

            return {
              success: false,
              error: errorMessage || error,
              code: (response as any).code,
            };
          }
        } else {
          // 直接返回数据的响应
          if (showSuccessToast && successMessage) {
            toast.success(successMessage);
          }
          
          return {
            success: true,
            data: response as T,
          };
        }
      }

      // 其他类型的响应
      if (showSuccessToast && successMessage) {
        toast.success(successMessage);
      }
      
      return {
        success: true,
        data: response as T,
      };

    } catch (error) {
      lastError = error;
      
      // 如果是最后一次尝试或者是客户端错误，不再重试
      if (attempt === retryCount || (error instanceof ApiClientError && error.statusCode < 500)) {
        break;
      }
      
      // 等待后重试
      if (attempt < retryCount) {
        await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)));
      }
    }
  }

  // 清除加载状态
  if (loadingKey) {
    LoadingManager.setLoading(loadingKey, false);
  }

  // 处理错误
  return handleApiError(lastError, {
    showErrorToast,
    errorMessage,
    redirectOnAuth
  });
}

/**
 * 处理API错误
 */
function handleApiError(
  error: any,
  config: {
    showErrorToast?: boolean;
    errorMessage?: string;
    redirectOnAuth?: boolean;
  }
): ApiCallResult<any> {
  const { showErrorToast = true, errorMessage, redirectOnAuth = true } = config;

  let finalError = '操作失败'; // 最基本的错误提示，只在完全无法获取错误信息时使用
  let code: string | undefined;

  // ApiClientError - 直接使用后端返回的错误消息
  if (error instanceof ApiClientError) {
    finalError = error.message; // 直接使用后端返回的消息
    code = error.code;

    // 处理认证错误 - 这里的消息也应该来自后端
    if (error.statusCode === 401 && redirectOnAuth) {
      if (showErrorToast) {
        // 使用后端返回的错误消息，而不是硬编码
        toast.error(error.message);
      }

      // 延迟跳转到登录页
      setTimeout(() => {
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      }, 1000);

      return {
        success: false,
        error: error.message, // 使用后端返回的消息
        code: error.code || 'UNAUTHORIZED'
      };
    }
  }
  // 标准Error对象
  else if (error instanceof Error) {
    finalError = error.message;
  }
  // 自定义错误对象 - 直接使用后端返回的错误消息
  else if (error && typeof error === 'object') {
    finalError = error.message || error.error || error.msg || '操作失败';
    code = error.code;
  }
  // 字符串错误
  else if (typeof error === 'string') {
    finalError = error;
  }

  // 使用自定义错误消息或原始错误消息
  const displayError = errorMessage || finalError;

  // 显示错误提示
  if (showErrorToast) {
    toast.error(displayError);
  }

  return {
    success: false,
    error: displayError,
    code
  };
}

/**
 * 批量API调用
 * 并行执行多个API调用，统一处理结果
 */
export async function batchApiCall<T extends Record<string, () => Promise<any>>>(
  apiCalls: T,
  config: ApiCallConfig = {}
): Promise<{
  [K in keyof T]: ApiCallResult<Awaited<ReturnType<T[K]>>>;
}> {
  const { loadingKey } = config;

  // 设置加载状态
  if (loadingKey) {
    LoadingManager.setLoading(loadingKey, true);
  }

  try {
    const promises = Object.entries(apiCalls).map(async ([key, apiFunction]) => {
      const result = await apiCall(apiFunction, { ...config, loadingKey: undefined });
      return [key, result] as const;
    });

    const results = await Promise.all(promises);
    
    // 清除加载状态
    if (loadingKey) {
      LoadingManager.setLoading(loadingKey, false);
    }

    return Object.fromEntries(results) as any;
  } catch (error) {
    // 清除加载状态
    if (loadingKey) {
      LoadingManager.setLoading(loadingKey, false);
    }
    
    throw error;
  }
}

/**
 * 创建带有默认配置的API调用函数
 */
export function createApiCaller(defaultConfig: ApiCallConfig) {
  return function<T>(
    apiFunction: () => Promise<ApiResponse<T>>,
    config: ApiCallConfig = {}
  ): Promise<ApiCallResult<T>> {
    return apiCall(apiFunction, { ...defaultConfig, ...config });
  };
}

/**
 * 常用的API调用配置预设
 */
export const ApiPresets = {
  // 静默调用（不显示任何提示）
  silent: {
    showSuccessToast: false,
    showErrorToast: false,
  },
  
  // 只显示错误提示
  errorOnly: {
    showSuccessToast: false,
    showErrorToast: true,
  },
  
  // 显示成功和错误提示
  withToast: {
    showSuccessToast: true,
    showErrorToast: true,
  },
  
  // 创建操作
  create: {
    showSuccessToast: true,
    successMessage: '创建成功',
    showErrorToast: true,
  },
  
  // 更新操作
  update: {
    showSuccessToast: true,
    successMessage: '更新成功',
    showErrorToast: true,
  },
  
  // 删除操作
  delete: {
    showSuccessToast: true,
    successMessage: '删除成功',
    showErrorToast: true,
  },
} as const;
