import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth, User, Session } from './auth';
import { db } from './database';
import { ClientDetector, ClientInfo, ClientPermission<PERSON>he<PERSON>, ClientType } from './client-detection';

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// API Error types
export class ApiError extends Error {
  constructor(
    public statusCode: number,
    message: string,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Success response helper
export function successResponse<T>(
  data?: T,
  message?: string,
  pagination?: ApiResponse['pagination']
): NextResponse<ApiResponse<T>> {
  return NextResponse.json({
    success: true,
    data,
    message,
    pagination,
  });
}

// Error response helper
export function errorResponse(
  error: string | Error,
  statusCode: number = 500,
  code?: string
): NextResponse<ApiResponse> {
  const message = error instanceof Error ? error.message : error;
  
  return NextResponse.json(
    {
      success: false,
      error: message,
      code,
    },
    { status: statusCode }
  );
}

// Validation error response
export function validationErrorResponse(
  errors: Record<string, string>
): NextResponse<ApiResponse> {
  return NextResponse.json(
    {
      success: false,
      error: 'Validation failed',
      data: errors,
    },
    { status: 400 }
  );
}

// Authentication middleware with client detection
export async function withAuth(
  request: NextRequest,
  handler: (request: NextRequest, user: User, session: Session, clientInfo: ClientInfo) => Promise<NextResponse>
): Promise<NextResponse> {
  try {
    // 检测客户端信息
    const clientInfo = ClientDetector.getClientInfo(request);

    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return errorResponse('缺少或无效的授权头', 401);
    }

    const token = authHeader.substring(7);
    const auth = await verifyAuth(token);

    if (!auth) {
      return errorResponse('无效或过期的令牌', 401);
    }

    // 检查客户端权限
    if (!ClientDetector.isTrustedClient(clientInfo.type)) {
      return errorResponse('不受信任的客户端', 403);
    }

    // Log API access with client info
    await logAuditEvent({
      userId: auth.user.id,
      action: 'API_ACCESS',
      resource: request.nextUrl.pathname,
      details: {
        method: request.method,
        userAgent: request.headers.get('user-agent'),
        clientType: clientInfo.type,
        clientVersion: clientInfo.version,
        clientPlatform: clientInfo.platform,
      },
      ipAddress: clientInfo.ipAddress,
    });

    return handler(request, auth.user, auth.session, clientInfo);
  } catch (error) {
    console.error('Auth middleware error:', error);
    return errorResponse('服务器内部错误', 500);
  }
}

// Role-based access control with client detection
export function withRole(
  roles: string[]
) {
  return async (
    request: NextRequest,
    handler: (request: NextRequest, user: User, session: Session, clientInfo: ClientInfo) => Promise<NextResponse>
  ): Promise<NextResponse> => {
    return withAuth(request, async (req, user, session, clientInfo) => {
      if (!roles.includes(user.role)) {
        return errorResponse('权限不足', 403);
      }
      return handler(req, user, session, clientInfo);
    });
  };
}

// Admin-only access control (only for admin web clients)
export function withAdminAccess() {
  return async (
    request: NextRequest,
    handler: (request: NextRequest, user: User, session: Session, clientInfo: ClientInfo) => Promise<NextResponse>
  ): Promise<NextResponse> => {
    return withAuth(request, async (req, user, session, clientInfo) => {
      // 检查是否为管理员角色
      if (user.role !== 'admin') {
        return errorResponse('需要管理员权限', 403);
      }

      // 检查是否为管理端客户端
      if (!ClientPermissionChecker.canAccessAdminAPI(clientInfo.type)) {
        return errorResponse('此客户端无法访问管理API', 403);
      }

      return handler(req, user, session, clientInfo);
    });
  };
}

// User API access control (for both admin and user clients)
export function withUserAccess() {
  return async (
    request: NextRequest,
    handler: (request: NextRequest, user: User, session: Session, clientInfo: ClientInfo) => Promise<NextResponse>
  ): Promise<NextResponse> => {
    return withAuth(request, async (req, user, session, clientInfo) => {
      // 检查客户端是否可以访问用户API
      if (!ClientPermissionChecker.canAccessUserAPI(clientInfo.type)) {
        return errorResponse('此客户端无法访问用户API', 403);
      }

      return handler(req, user, session, clientInfo);
    });
  };
}

// Request validation
export function validateRequest<T>(
  data: any,
  schema: Record<string, (value: any) => string | null>
): { isValid: boolean; errors: Record<string, string>; data: T } {
  const errors: Record<string, string> = {};
  const validatedData: any = {};

  for (const [field, validator] of Object.entries(schema)) {
    const error = validator(data[field]);
    if (error) {
      errors[field] = error;
    } else {
      validatedData[field] = data[field];
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    data: validatedData as T,
  };
}

// Common validators
export const validators = {
  required: (value: any) => {
    if (value === undefined || value === null || value === '') {
      return '此字段为必填项';
    }
    return null;
  },

  email: (value: string) => {
    if (!value) return null;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value) ? null : '邮箱格式无效';
  },

  minLength: (min: number) => (value: string) => {
    if (!value) return null;
    return value.length >= min ? null : `至少需要 ${min} 个字符`;
  },

  maxLength: (max: number) => (value: string) => {
    if (!value) return null;
    return value.length <= max ? null : `不能超过 ${max} 个字符`;
  },

  oneOf: (options: string[]) => (value: string) => {
    if (!value) return null;
    return options.includes(value) ? null : `必须是以下选项之一：${options.join(', ')}`;
  },

  numeric: (value: any) => {
    if (value === undefined || value === null) return null;
    return !isNaN(Number(value)) ? null : '必须是数字';
  },

  boolean: (value: any) => {
    if (value === undefined || value === null) return null;
    return typeof value === 'boolean' ? null : '必须是布尔值';
  },
};

// Pagination helper
export function getPaginationParams(request: NextRequest): {
  page: number;
  limit: number;
  offset: number;
} {
  const url = new URL(request.url);
  const page = Math.max(1, parseInt(url.searchParams.get('page') || '1'));
  const limit = Math.min(100, Math.max(1, parseInt(url.searchParams.get('limit') || '10')));
  const offset = (page - 1) * limit;

  return { page, limit, offset };
}

// Get client IP address
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

// Audit logging
export interface AuditLogData {
  userId?: number;
  action: string;
  resource?: string;
  resourceId?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
}

export async function logAuditEvent(data: AuditLogData): Promise<void> {
  try {
    await db.query(
      `INSERT INTO audit_logs (user_id, action, resource, resource_id, details, ip_address, user_agent)
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        data.userId || null,
        data.action,
        data.resource || null,
        data.resourceId || null,
        data.details ? JSON.stringify(data.details) : null,
        data.ipAddress || null,
        data.userAgent || null,
      ]
    );
  } catch (error) {
    console.error('Failed to log audit event:', error);
  }
}

// Rate limiting helper (simple in-memory implementation)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export function rateLimit(
  key: string,
  maxRequests: number = 100,
  windowMs: number = 60000
): boolean {
  const now = Date.now();
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= maxRequests) {
    return false;
  }

  record.count++;
  return true;
}

// Clean up expired rate limit records
setInterval(() => {
  const now = Date.now();
  for (const [key, record] of rateLimitStore.entries()) {
    if (now > record.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 60000); // Clean up every minute
