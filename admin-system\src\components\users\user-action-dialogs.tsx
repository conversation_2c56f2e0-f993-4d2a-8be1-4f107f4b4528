"use client"

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { User } from '@/services/auth.service';

interface BanUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: User | null;
  onConfirm: (reason: string, expiresAt?: Date) => void;
  loading?: boolean;
}

export function BanUserDialog({ open, onOpenChange, user, onConfirm, loading }: BanUserDialogProps) {
  const [reason, setReason] = useState('');
  const [expiresAt, setExpiresAt] = useState<Date>();
  const [banType, setBanType] = useState<'permanent' | 'temporary'>('permanent');

  const handleSubmit = () => {
    if (!reason.trim()) return;
    onConfirm(reason, banType === 'temporary' ? expiresAt : undefined);
    setReason('');
    setExpiresAt(undefined);
    setBanType('permanent');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>封禁用户</DialogTitle>
          <DialogDescription>
            确定要封禁用户 <span className="font-medium">{user?.username}</span> 吗？
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label>封禁类型</Label>
            <Select value={banType} onValueChange={(value: 'permanent' | 'temporary') => setBanType(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="permanent">永久封禁</SelectItem>
                <SelectItem value="temporary">临时封禁</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {banType === 'temporary' && (
            <div className="space-y-2">
              <Label>解封时间</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !expiresAt && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {expiresAt ? format(expiresAt, "PPP") : "选择解封日期"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={expiresAt}
                    onSelect={setExpiresAt}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="reason">封禁原因</Label>
            <Textarea
              id="reason"
              placeholder="请输入封禁原因..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={3}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={!reason.trim() || loading}
            variant="destructive"
          >
            {loading ? '处理中...' : '确认封禁'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

interface SetVipDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: User | null;
  onConfirm: (level: string, expiresAt?: Date) => void;
  loading?: boolean;
}

export function SetVipDialog({ open, onOpenChange, user, onConfirm, loading }: SetVipDialogProps) {
  const [level, setLevel] = useState<string>('v1');
  const [expiresAt, setExpiresAt] = useState<Date>();
  const [vipType, setVipType] = useState<'permanent' | 'temporary'>('temporary');

  const handleSubmit = () => {
    onConfirm(level, vipType === 'temporary' ? expiresAt : undefined);
    setLevel('v1');
    setExpiresAt(undefined);
    setVipType('temporary');
  };

  const vipLevels = [
    { value: 'none', label: '取消VIP', color: '#6B7280' },
    { value: 'v1', label: 'VIP V1', color: '#10B981' },
    { value: 'v2', label: 'VIP V2', color: '#3B82F6' },
    { value: 'v3', label: 'VIP V3', color: '#8B5CF6' },
    { value: 'v4', label: 'VIP V4', color: '#F59E0B' },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>设置VIP等级</DialogTitle>
          <DialogDescription>
            为用户 <span className="font-medium">{user?.username}</span> 设置VIP等级
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label>VIP等级</Label>
            <Select value={level} onValueChange={setLevel}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {vipLevels.map((vipLevel) => (
                  <SelectItem key={vipLevel.value} value={vipLevel.value}>
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: vipLevel.color }}
                      ></div>
                      <span>{vipLevel.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {level !== 'none' && (
            <>
              <div className="space-y-2">
                <Label>VIP类型</Label>
                <Select value={vipType} onValueChange={(value: 'permanent' | 'temporary') => setVipType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="temporary">临时VIP</SelectItem>
                    <SelectItem value="permanent">永久VIP</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {vipType === 'temporary' && (
                <div className="space-y-2">
                  <Label>到期时间</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !expiresAt && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {expiresAt ? format(expiresAt, "PPP") : "选择到期日期"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={expiresAt}
                        onSelect={setExpiresAt}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              )}
            </>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={loading || (vipType === 'temporary' && level !== 'none' && !expiresAt)}
          >
            {loading ? '处理中...' : '确认设置'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
