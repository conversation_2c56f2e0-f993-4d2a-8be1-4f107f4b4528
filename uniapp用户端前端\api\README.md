# UniApp API 封装使用说明

## 概述

本项目提供了完整的API封装解决方案，实现了高内聚低耦合的设计原则，包含以下特性：

- 统一的HTTP请求封装
- 自动token管理
- 请求/响应拦截器
- 错误处理和重试机制
- 统一的提示管理
- 接口分离和模块化管理

## 文件结构

```
api/
├── index.js          # API接口定义
└── README.md         # 使用说明

utils/
└── api.js            # API核心封装
```

## 基础使用

### 1. 导入API

```javascript
// 导入所有API
import API from '@/api/index.js';

// 或者按需导入
import { authAPI, userAPI } from '@/api/index.js';
```

### 2. 基础请求

```javascript
// 使用封装好的API
try {
  const result = await API.auth.login({
    emailOrUsername: '<EMAIL>',
    password: 'password123'
  });
  
  console.log('登录成功:', result);
  // 自动保存token，后续请求会自动携带
} catch (error) {
  console.error('登录失败:', error.message);
  // 错误已自动显示toast提示
}
```

### 3. 直接使用api工具

```javascript
import api from '@/utils/api.js';

// GET请求
const users = await api.get('/users', { page: 1, limit: 10 });

// POST请求
const result = await api.post('/users', { name: '张三', email: '<EMAIL>' });

// PUT请求
const updated = await api.put('/users/1', { name: '李四' });

// DELETE请求
await api.delete('/users/1');
```

## 高级功能

### 1. Token管理

```javascript
import api from '@/utils/api.js';

// 获取token
const token = api.TokenManager.getToken();

// 设置token
api.TokenManager.setToken('your-jwt-token');

// 清除token
api.TokenManager.clearToken();
```

### 2. 统一提示

```javascript
import api from '@/utils/api.js';

// 成功提示
api.ToastManager.success('操作成功');

// 错误提示
api.ToastManager.error('操作失败');

// 加载提示
api.ToastManager.loading('处理中...');
api.ToastManager.hideLoading();

// 确认对话框
const confirmed = await api.ToastManager.confirm({
  title: '删除确认',
  content: '确定要删除这条记录吗？'
});

if (confirmed) {
  // 执行删除操作
}
```

### 3. 文件上传

```javascript
import { uploadAPI } from '@/api/index.js';

// 选择并上传图片
uni.chooseImage({
  count: 1,
  success: async (res) => {
    try {
      const result = await uploadAPI.uploadImage(res.tempFilePaths[0], {
        category: 'avatar'
      });
      
      console.log('上传成功:', result.data.url);
    } catch (error) {
      console.error('上传失败:', error.message);
    }
  }
});
```

### 4. 错误处理

```javascript
// 全局错误处理已内置，也可以自定义处理
try {
  const result = await API.user.getUserById(123);
} catch (error) {
  switch (error.code) {
    case 'UNAUTHORIZED':
      // 未授权，已自动跳转登录页
      break;
    case 'NETWORK_ERROR':
      // 网络错误
      console.log('网络连接失败');
      break;
    case 'BUSINESS_ERROR':
      // 业务错误
      console.log('业务处理失败:', error.message);
      break;
    default:
      console.log('未知错误:', error.message);
  }
}
```

## 页面使用示例

### 登录页面

```javascript
// pages/login/login.vue
<script>
import { authAPI } from '@/api/index.js';

export default {
  data() {
    return {
      form: {
        emailOrUsername: '',
        password: ''
      },
      loading: false
    };
  },
  
  methods: {
    async handleLogin() {
      if (!this.form.emailOrUsername || !this.form.password) {
        uni.showToast({
          title: '请填写完整信息',
          icon: 'none'
        });
        return;
      }
      
      this.loading = true;
      
      try {
        const result = await authAPI.login(this.form);
        
        // 登录成功，跳转到首页
        uni.reLaunch({
          url: '/pages/index/index'
        });
        
      } catch (error) {
        // 错误已自动显示，这里可以做额外处理
        console.error('登录失败:', error);
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>
```

### 用户列表页面

```javascript
// pages/users/users.vue
<script>
import { userAPI } from '@/api/index.js';

export default {
  data() {
    return {
      users: [],
      loading: false,
      pagination: {
        page: 1,
        limit: 10,
        total: 0
      }
    };
  },
  
  onLoad() {
    this.loadUsers();
  },
  
  methods: {
    async loadUsers() {
      this.loading = true;
      
      try {
        const result = await userAPI.getUsers({
          page: this.pagination.page,
          limit: this.pagination.limit
        });
        
        this.users = result.data;
        this.pagination.total = result.pagination.total;
        
      } catch (error) {
        console.error('加载用户列表失败:', error);
      } finally {
        this.loading = false;
      }
    },
    
    async deleteUser(id) {
      const confirmed = await this.$api.ToastManager.confirm({
        title: '删除确认',
        content: '确定要删除这个用户吗？'
      });
      
      if (!confirmed) return;
      
      try {
        await userAPI.deleteUser(id);
        
        // 重新加载列表
        this.loadUsers();
        
        this.$api.ToastManager.success('删除成功');
        
      } catch (error) {
        console.error('删除失败:', error);
      }
    }
  }
};
</script>
```

## 配置说明

### API配置

在 `utils/api.js` 中可以修改以下配置：

```javascript
const API_CONFIG = {
  DEV_BASE_URL: 'http://localhost:3000/api',    // 开发环境API地址
  PROD_BASE_URL: 'https://your-domain.com/api', // 生产环境API地址
  TIMEOUT: 10000,                               // 请求超时时间
  RETRY_COUNT: 3,                               // 重试次数
  RETRY_DELAY: 1000                             // 重试延迟时间
};
```

### 动态设置API地址

```javascript
import api from '@/utils/api.js';

// 动态设置API基础地址
api.setBaseUrl('https://new-api-domain.com/api');
```

## 最佳实践

1. **接口分离**: 不同业务模块的API分别定义，避免耦合
2. **错误处理**: 统一的错误处理机制，减少重复代码
3. **Token管理**: 自动管理用户认证状态
4. **加载状态**: 合理使用loading状态提升用户体验
5. **重试机制**: 网络不稳定时自动重试
6. **类型安全**: 建议配合TypeScript使用以获得更好的开发体验

## 注意事项

1. 确保在 `main.js` 中正确配置API基础地址
2. 登录成功后token会自动保存，无需手动管理
3. 401错误会自动清除token并跳转登录页
4. 所有网络错误都会显示用户友好的提示信息
5. 上传文件时注意文件大小限制
