"use client"

import { withAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Settings, Shield, Database, Bell, Palette, Globe } from 'lucide-react';

function SettingsPage() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">系统设置</h2>
          <p className="text-muted-foreground">
            管理系统配置和偏好设置
          </p>
        </div>
      </div>

      <div className="grid gap-6">
        {/* 基本设置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              基本设置
            </CardTitle>
            <CardDescription>
              配置系统的基本信息和行为
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="site-name">网站名称</Label>
                <Input
                  id="site-name"
                  placeholder="管理系统"
                  defaultValue="管理系统"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="site-url">网站URL</Label>
                <Input
                  id="site-url"
                  placeholder="https://example.com"
                  defaultValue="https://admin.example.com"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">网站描述</Label>
              <Input
                id="description"
                placeholder="网站描述"
                defaultValue="基于 Next.js 15 和 shadcn/ui 的现代化管理仪表板"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="timezone">时区</Label>
              <Select defaultValue="asia-shanghai">
                <SelectTrigger>
                  <SelectValue placeholder="选择时区" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="asia-shanghai">亚洲/上海 (UTC+8)</SelectItem>
                  <SelectItem value="utc">UTC (UTC+0)</SelectItem>
                  <SelectItem value="america-new-york">美洲/纽约 (UTC-5)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* 安全设置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              安全设置
            </CardTitle>
            <CardDescription>
              配置系统安全相关选项
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>启用双因素认证</Label>
                <p className="text-sm text-muted-foreground">
                  为管理员账户启用额外的安全验证
                </p>
              </div>
              <Switch defaultChecked />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>强制HTTPS</Label>
                <p className="text-sm text-muted-foreground">
                  自动将HTTP请求重定向到HTTPS
                </p>
              </div>
              <Switch defaultChecked />
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="session-timeout">会话超时时间（分钟）</Label>
                <Input
                  id="session-timeout"
                  type="number"
                  placeholder="30"
                  defaultValue="30"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="max-login-attempts">最大登录尝试次数</Label>
                <Input
                  id="max-login-attempts"
                  type="number"
                  placeholder="5"
                  defaultValue="5"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 通知设置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              通知设置
            </CardTitle>
            <CardDescription>
              配置系统通知和提醒
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>邮件通知</Label>
                <p className="text-sm text-muted-foreground">
                  接收重要系统事件的邮件通知
                </p>
              </div>
              <Switch defaultChecked />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>浏览器通知</Label>
                <p className="text-sm text-muted-foreground">
                  在浏览器中显示实时通知
                </p>
              </div>
              <Switch />
            </div>

            <Separator />

            <div className="space-y-2">
              <Label htmlFor="admin-email">管理员邮箱</Label>
              <Input
                id="admin-email"
                type="email"
                placeholder="<EMAIL>"
                defaultValue="<EMAIL>"
              />
            </div>
          </CardContent>
        </Card>

        {/* 外观设置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              外观设置
            </CardTitle>
            <CardDescription>
              自定义系统界面外观
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="theme">主题</Label>
              <Select defaultValue="system">
                <SelectTrigger>
                  <SelectValue placeholder="选择主题" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">浅色</SelectItem>
                  <SelectItem value="dark">深色</SelectItem>
                  <SelectItem value="system">跟随系统</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="language">语言</Label>
              <Select defaultValue="zh-cn">
                <SelectTrigger>
                  <SelectValue placeholder="选择语言" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="zh-cn">简体中文</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="ja">日本語</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>紧凑模式</Label>
                <p className="text-sm text-muted-foreground">
                  使用更紧凑的界面布局
                </p>
              </div>
              <Switch />
            </div>
          </CardContent>
        </Card>

        {/* 保存按钮 */}
        <div className="flex justify-end">
          <Button size="lg">
            保存设置
          </Button>
        </div>
      </div>
    </div>
  );
}

export default withAuth(SettingsPage);
