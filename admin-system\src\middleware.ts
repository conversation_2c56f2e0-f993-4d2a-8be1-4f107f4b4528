import { NextRequest, NextResponse } from 'next/server';

/**
 * CORS中间件配置
 * 基于Next.js 15最佳实践，为UniApp等跨域客户端提供API访问支持
 */

// 从环境变量获取允许的源地址，支持多环境配置
function getAllowedOrigins(): string[] {
  const envOrigins = process.env.CORS_ALLOWED_ORIGINS;
  if (envOrigins) {
    return envOrigins.split(',').map(origin => origin.trim());
  }

  // 默认开发环境配置
  const defaultOrigins = [
    'http://localhost:5173',  // UniApp H5开发环境
    'http://localhost:3000',  // Next.js开发环境
    'http://localhost:3333',  // 后端开发环境
  ];

  // 生产环境添加域名
  if (process.env.NODE_ENV === 'production' && process.env.PRODUCTION_DOMAIN) {
    defaultOrigins.push(`https://${process.env.PRODUCTION_DOMAIN}`);
  }

  return defaultOrigins;
}

// CORS配置选项
const corsOptions = {
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With, X-Client-Type, X-Client-Version',
  'Access-Control-Allow-Credentials': 'true',
  'Access-Control-Max-Age': '86400', // 24小时预检缓存
  'Access-Control-Expose-Headers': 'X-Total-Count, X-Page-Count', // 暴露分页信息头
} as const;

// 简单的内存级请求频率限制（生产环境建议使用Redis）
const requestCounts = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(ip: string): boolean {
  // 从环境变量获取配置，提供默认值
  const maxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100');
  const windowMs = parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000');

  const now = Date.now();
  const key = `rate_limit:${ip}`;
  const record = requestCounts.get(key);

  if (!record || now > record.resetTime) {
    // 重置计数器
    requestCounts.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= maxRequests) {
    return false; // 超过限制
  }

  record.count++;
  return true;
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const origin = request.headers.get('origin') ?? '';
  const referer = request.headers.get('referer') ?? '';
  const userAgent = request.headers.get('user-agent') ?? '';
  const allowedOrigins = getAllowedOrigins();
  const isAllowedOrigin = allowedOrigins.includes(origin);

  // 获取客户端IP地址
  const ip = request.headers.get('x-forwarded-for')?.split(',')[0] ||
    request.headers.get('x-real-ip') ||
    request.headers.get('cf-connecting-ip') || // Cloudflare
    'unknown';

  // 请求频率限制（生产环境启用）
  if (process.env.NODE_ENV === 'production' && !checkRateLimit(ip)) {
    const maxRequests = process.env.RATE_LIMIT_MAX_REQUESTS || '100';
    const windowMs = parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000');
    const retryAfterSeconds = Math.ceil(windowMs / 1000);

    console.warn(`[CORS] Rate limit exceeded for IP: ${ip}`);
    return new NextResponse('请求过于频繁，请稍后再试', {
      status: 429,
      headers: {
        'Retry-After': retryAfterSeconds.toString(),
        'X-RateLimit-Limit': maxRequests,
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': new Date(Date.now() + windowMs).toISOString(),
        'Content-Type': 'text/plain; charset=utf-8',
      }
    });
  }

  // 开发环境日志
  if (process.env.NODE_ENV === 'development') {
    console.log(`[CORS] ${request.method} ${pathname} from ${origin || 'same-origin'}`);
  }

  // 安全检查：阻止可疑请求
  if (origin && !isAllowedOrigin) {
    // 记录可疑的跨域请求
    console.warn(`[CORS] Blocked request from unauthorized origin: ${origin} to ${pathname}`);

    // 生产环境下直接拒绝
    if (process.env.NODE_ENV === 'production') {
      return new NextResponse('Forbidden', { status: 403 });
    }
  }

  // 检查Referer头（仅对敏感API路径）
  const sensitiveApiPaths = ['/api/admin/', '/api/users/', '/api/activation-codes/'];
  const isSensitiveApi = sensitiveApiPaths.some(path => pathname.startsWith(path));

  if (isSensitiveApi && origin && referer) {
    try {
      const refererUrl = new URL(referer);
      const refererOrigin = `${refererUrl.protocol}//${refererUrl.host}`;

      // Referer的origin必须与请求的origin匹配
      if (refererOrigin !== origin) {
        console.warn(`[CORS] Referer mismatch: origin=${origin}, referer=${refererOrigin}`);
        if (process.env.NODE_ENV === 'production') {
          return new NextResponse('Forbidden', { status: 403 });
        }
      }
    } catch (error) {
      // Referer格式无效
      console.warn(`[CORS] Invalid referer format: ${referer}`);
    }
  }

  // 检查User-Agent（阻止明显的机器人请求）
  const suspiciousUserAgents = ['curl', 'wget', 'python-requests', 'postman'];
  const isSuspiciousUserAgent = suspiciousUserAgents.some(agent =>
    userAgent.toLowerCase().includes(agent)
  );

  if (isSuspiciousUserAgent && process.env.NODE_ENV === 'production') {
    console.warn(`[CORS] Blocked suspicious user agent: ${userAgent}`);
    return new NextResponse('Forbidden', { status: 403 });
  }

  // 处理预检请求 (OPTIONS)
  if (request.method === 'OPTIONS') {
    const preflightHeaders: Record<string, string> = {
      ...corsOptions,
    };

    // 只为允许的源设置Access-Control-Allow-Origin
    if (isAllowedOrigin) {
      preflightHeaders['Access-Control-Allow-Origin'] = origin;
    }

    return new NextResponse(null, {
      status: 200,
      headers: preflightHeaders,
    });
  }

  // 处理普通请求
  const response = NextResponse.next();

  // 设置CORS headers
  if (isAllowedOrigin) {
    response.headers.set('Access-Control-Allow-Origin', origin);
  }

  // 设置其他CORS headers
  Object.entries(corsOptions).forEach(([key, value]) => {
    if (key !== 'Access-Control-Allow-Origin') {
      response.headers.set(key, value);
    }
  });

  // 添加安全headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
}

// 优化的matcher配置 - 基于Next.js 15最佳实践
export const config = {
  matcher: [
    /*
     * 匹配所有API路由，但排除：
     * - _next/static (静态文件)
     * - _next/image (图片优化)
     * - favicon.ico (网站图标)
     * - sitemap.xml, robots.txt (SEO文件)
     * - 预检请求的prefetch headers
     */
    {
      source: '/api/:path*',
      missing: [
        { type: 'header', key: 'next-router-prefetch' },
        { type: 'header', key: 'purpose', value: 'prefetch' },
      ],
    },
  ],
};
