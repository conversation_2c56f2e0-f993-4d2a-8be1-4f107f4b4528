/**
 * 用户状态管理工具
 * 提供用户登录状态管理和持久化存储
 */

import api from './api.js';

const { TokenManager } = api;

// 存储键名
const STORAGE_KEYS = {
  USER_INFO: 'user_info',
  LOGIN_STATUS: 'login_status',
  LAST_LOGIN_TIME: 'last_login_time'
};

// 用户状态管理器
export class UserManager {
  constructor() {
    this.userInfo = null;
    this.isLoggedIn = false;
    this.listeners = [];
    
    // 初始化时从本地存储恢复状态
    this.restoreFromStorage();
  }

  // 从本地存储恢复用户状态
  restoreFromStorage() {
    try {
      const userInfo = uni.getStorageSync(STORAGE_KEYS.USER_INFO);
      const loginStatus = uni.getStorageSync(STORAGE_KEYS.LOGIN_STATUS);
      const token = TokenManager.getToken();

      if (userInfo && loginStatus && token) {
        this.userInfo = JSON.parse(userInfo);
        this.isLoggedIn = true;
        this.notifyListeners();
      }
    } catch (error) {
      console.error('恢复用户状态失败:', error);
      this.clearUserData();
    }
  }

  // 设置用户信息
  setUserInfo(userInfo) {
    this.userInfo = userInfo;
    this.isLoggedIn = true;
    
    // 保存到本地存储
    try {
      uni.setStorageSync(STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
      uni.setStorageSync(STORAGE_KEYS.LOGIN_STATUS, true);
      uni.setStorageSync(STORAGE_KEYS.LAST_LOGIN_TIME, Date.now());
    } catch (error) {
      console.error('保存用户信息失败:', error);
    }
    
    this.notifyListeners();
  }

  // 获取用户信息
  getUserInfo() {
    return this.userInfo;
  }

  // 检查是否已登录
  isUserLoggedIn() {
    return this.isLoggedIn && !!TokenManager.getToken();
  }

  // 获取用户ID
  getUserId() {
    return this.userInfo?.id || null;
  }

  // 获取用户名
  getUsername() {
    return this.userInfo?.username || '';
  }

  // 获取用户邮箱
  getUserEmail() {
    return this.userInfo?.email || '';
  }

  // 获取用户角色
  getUserRole() {
    return this.userInfo?.role || 'user';
  }

  // 获取VIP等级
  getVipLevel() {
    return this.userInfo?.vip_level || 'none';
  }

  // 检查是否为VIP用户
  isVipUser() {
    const vipLevel = this.getVipLevel();
    return vipLevel && vipLevel !== 'none';
  }

  // 获取VIP过期时间
  getVipExpiresAt() {
    return this.userInfo?.vip_expires_at || null;
  }

  // 检查VIP是否过期
  isVipExpired() {
    const expiresAt = this.getVipExpiresAt();
    if (!expiresAt) return true;
    
    return new Date(expiresAt) < new Date();
  }

  // 更新用户信息
  updateUserInfo(updates) {
    if (!this.userInfo) return;
    
    this.userInfo = { ...this.userInfo, ...updates };
    this.setUserInfo(this.userInfo);
  }

  // 清除用户数据
  clearUserData() {
    this.userInfo = null;
    this.isLoggedIn = false;
    
    // 清除本地存储
    try {
      uni.removeStorageSync(STORAGE_KEYS.USER_INFO);
      uni.removeStorageSync(STORAGE_KEYS.LOGIN_STATUS);
      uni.removeStorageSync(STORAGE_KEYS.LAST_LOGIN_TIME);
    } catch (error) {
      console.error('清除用户数据失败:', error);
    }
    
    // 清除token
    TokenManager.clearToken();
    
    this.notifyListeners();
  }

  // 用户登录
  async login(userInfo, token) {
    // 保存token
    TokenManager.setToken(token);
    
    // 保存用户信息
    this.setUserInfo(userInfo);
    
    return {
      success: true,
      user: userInfo
    };
  }

  // 用户登出
  async logout() {
    this.clearUserData();
    
    return {
      success: true
    };
  }

  // 添加状态变化监听器
  addListener(listener) {
    this.listeners.push(listener);
    
    // 返回取消监听的函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  // 通知所有监听器
  notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener({
          userInfo: this.userInfo,
          isLoggedIn: this.isLoggedIn
        });
      } catch (error) {
        console.error('通知监听器失败:', error);
      }
    });
  }

  // 检查登录状态是否有效
  async validateLoginStatus() {
    if (!this.isLoggedIn || !TokenManager.getToken()) {
      this.clearUserData();
      return false;
    }
    
    // 可以在这里添加token验证逻辑
    return true;
  }

  // 获取最后登录时间
  getLastLoginTime() {
    try {
      return uni.getStorageSync(STORAGE_KEYS.LAST_LOGIN_TIME) || null;
    } catch (error) {
      return null;
    }
  }

  // 格式化用户显示名称
  getDisplayName() {
    if (!this.userInfo) return '';
    
    return this.userInfo.full_name || 
           this.userInfo.username || 
           this.userInfo.email || 
           '未知用户';
  }

  // 获取用户头像URL
  getAvatarUrl() {
    return this.userInfo?.avatar_url || '';
  }

  // 检查用户权限
  hasPermission(permission) {
    const role = this.getUserRole();
    
    // 简单的权限检查逻辑
    const permissions = {
      admin: ['read', 'write', 'delete', 'manage'],
      moderator: ['read', 'write'],
      user: ['read']
    };
    
    return permissions[role]?.includes(permission) || false;
  }
}

// 创建全局用户管理器实例
const userManager = new UserManager();

// 导出便捷方法
export const userUtils = {
  // 获取用户管理器实例
  getInstance: () => userManager,
  
  // 快捷方法
  isLoggedIn: () => userManager.isUserLoggedIn(),
  getUserInfo: () => userManager.getUserInfo(),
  getUsername: () => userManager.getUsername(),
  getVipLevel: () => userManager.getVipLevel(),
  isVipUser: () => userManager.isVipUser(),
  
  // 登录登出
  login: (userInfo, token) => userManager.login(userInfo, token),
  logout: () => userManager.logout(),
  
  // 监听状态变化
  onStateChange: (listener) => userManager.addListener(listener)
};

export default userManager;
