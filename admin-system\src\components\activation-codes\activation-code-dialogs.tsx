"use client"

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, Plus, Copy } from 'lucide-react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { VIP_LEVEL_CONFIG, ActivationCode } from '@/services/activation-code.service';

// 创建激活码对话框
interface CreateActivationCodeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (data: {
    vip_level: string;
    vip_duration_days?: number;
    expires_at?: Date;
    description?: string;
    count?: number;
  }) => void;
  loading?: boolean;
}

export function CreateActivationCodeDialog({
  open,
  onOpenChange,
  onConfirm,
  loading
}: CreateActivationCodeDialogProps) {
  const [formData, setFormData] = useState({
    vip_level: 'v1',
    vip_duration_days: VIP_LEVEL_CONFIG.v1.default_duration,
    expires_at: undefined as Date | undefined,
    description: '',
    count: 1,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onConfirm({
      vip_level: formData.vip_level,
      vip_duration_days: formData.vip_duration_days,
      expires_at: formData.expires_at,
      description: formData.description || undefined,
      count: formData.count,
    });
  };

  const handleVipLevelChange = (level: string) => {
    const config = VIP_LEVEL_CONFIG[level as keyof typeof VIP_LEVEL_CONFIG];
    setFormData(prev => ({
      ...prev,
      vip_level: level,
      vip_duration_days: config?.default_duration || 30,
    }));
  };

  const handleReset = () => {
    setFormData({
      vip_level: 'v1',
      vip_duration_days: VIP_LEVEL_CONFIG.v1.default_duration,
      expires_at: undefined,
      description: '',
      count: 1,
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            创建激活码
          </DialogTitle>
          <DialogDescription>
            创建新的VIP激活码，可以单个创建或批量生成
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* VIP等级选择 */}
          <div className="space-y-2">
            <Label htmlFor="vip_level">VIP等级 *</Label>
            <Select value={formData.vip_level} onValueChange={handleVipLevelChange}>
              <SelectTrigger>
                <SelectValue placeholder="选择VIP等级" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(VIP_LEVEL_CONFIG).map(([key, config]) => (
                  <SelectItem key={key} value={key}>
                    <div className="flex items-center gap-2">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: config.color }}
                      ></div>
                      <span>{config.label}</span>
                      <span className="text-xs text-muted-foreground">
                        ({config.description})
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* VIP有效期 */}
          <div className="space-y-2">
            <Label htmlFor="vip_duration_days">VIP有效期（天）*</Label>
            <Input
              id="vip_duration_days"
              type="number"
              min="1"
              max="3650"
              value={formData.vip_duration_days}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                vip_duration_days: parseInt(e.target.value) || 30
              }))}
              placeholder="输入VIP有效期天数"
              required
            />
          </div>

          {/* 激活码过期时间 */}
          <div className="space-y-2">
            <Label>激活码过期时间（可选）</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !formData.expires_at && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formData.expires_at ? (
                    format(formData.expires_at, "PPP", { locale: zhCN })
                  ) : (
                    "选择过期时间"
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={formData.expires_at}
                  onSelect={(date) => setFormData(prev => ({ ...prev, expires_at: date }))}
                  disabled={(date) => date < new Date()}
                  initialFocus
                />
                <div className="p-3 border-t">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setFormData(prev => ({ ...prev, expires_at: undefined }))}
                    className="w-full"
                  >
                    清除日期
                  </Button>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* 生成数量 */}
          <div className="space-y-2">
            <Label htmlFor="count">生成数量 *</Label>
            <Input
              id="count"
              type="number"
              min="1"
              max="100"
              value={formData.count}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                count: parseInt(e.target.value) || 1
              }))}
              placeholder="输入生成数量"
              required
            />
            <p className="text-xs text-muted-foreground">
              单次最多可生成100个激活码
            </p>
          </div>

          {/* 描述 */}
          <div className="space-y-2">
            <Label htmlFor="description">描述（可选）</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="输入激活码描述或备注"
              rows={3}
            />
          </div>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                handleReset();
                onOpenChange(false);
              }}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? '创建中...' : `创建${formData.count > 1 ? ` ${formData.count} 个` : ''}激活码`}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}

// 激活码详情对话框
interface ActivationCodeDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  code: ActivationCode | null;
}

export function ActivationCodeDetailDialog({
  open,
  onOpenChange,
  code
}: ActivationCodeDetailDialogProps) {
  const [copied, setCopied] = useState(false);

  const handleCopyCode = async () => {
    if (!code) return;
    
    try {
      await navigator.clipboard.writeText(code.code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy code:', error);
    }
  };

  if (!code) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>激活码详情</DialogTitle>
          <DialogDescription>
            查看激活码的详细信息
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 激活码 */}
          <div className="space-y-2">
            <Label>激活码</Label>
            <div className="flex items-center gap-2">
              <code className="flex-1 text-sm font-mono bg-muted px-3 py-2 rounded border">
                {code.code}
              </code>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyCode}
                className="shrink-0"
              >
                <Copy className={`h-4 w-4 ${copied ? 'text-green-600' : ''}`} />
              </Button>
            </div>
          </div>

          {/* 基本信息 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>VIP等级</Label>
              <div className="text-sm">
                {VIP_LEVEL_CONFIG[code.vip_level]?.label || code.vip_level}
              </div>
            </div>
            <div className="space-y-2">
              <Label>VIP有效期</Label>
              <div className="text-sm">{code.vip_duration_days} 天</div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>状态</Label>
              <div className="text-sm">{code.status}</div>
            </div>
            <div className="space-y-2">
              <Label>创建时间</Label>
              <div className="text-sm">
                {format(new Date(code.created_at), "PPP", { locale: zhCN })}
              </div>
            </div>
          </div>

          {/* 使用信息 */}
          {code.status === 'used' && code.used_at && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>使用时间</Label>
                <div className="text-sm">
                  {format(new Date(code.used_at), "PPP", { locale: zhCN })}
                </div>
              </div>

              {code.user && (
                <div className="space-y-2">
                  <Label>使用者</Label>
                  <div className="text-sm space-y-1">
                    <div><strong>用户名:</strong> {code.user.username}</div>
                    <div><strong>邮箱:</strong> {code.user.email}</div>
                    {code.user.full_name && (
                      <div><strong>姓名:</strong> {code.user.full_name}</div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* 创建者信息 */}
          {code.creator && (
            <div className="space-y-2">
              <Label>创建者</Label>
              <div className="text-sm space-y-1">
                <div><strong>用户名:</strong> {code.creator.username}</div>
                <div><strong>邮箱:</strong> {code.creator.email}</div>
              </div>
            </div>
          )}

          {/* 过期时间 */}
          {code.expires_at && (
            <div className="space-y-2">
              <Label>过期时间</Label>
              <div className="text-sm">
                {format(new Date(code.expires_at), "PPP", { locale: zhCN })}
              </div>
            </div>
          )}

          {/* 描述 */}
          {code.description && (
            <div className="space-y-2">
              <Label>描述</Label>
              <div className="text-sm text-muted-foreground">
                {code.description}
              </div>
            </div>
          )}

          {/* 批次信息 */}
          {code.batch_id && (
            <div className="space-y-2">
              <Label>批次ID</Label>
              <div className="text-sm font-mono">
                {code.batch_id}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
