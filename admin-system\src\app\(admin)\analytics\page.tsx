"use client"

import { useState, useEffect } from 'react';
import { withAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Activity,
  Eye,
  Download,
  Calendar
} from 'lucide-react';

interface AnalyticsData {
  totalViews: number;
  uniqueVisitors: number;
  bounceRate: number;
  avgSessionDuration: string;
  topPages: Array<{
    page: string;
    views: number;
    change: number;
  }>;
  userGrowth: Array<{
    month: string;
    users: number;
    change: number;
  }>;
}

function AnalyticsPage() {
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');

  useEffect(() => {
    // 模拟加载分析数据
    const loadAnalytics = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const mockData: AnalyticsData = {
          totalViews: 45231,
          uniqueVisitors: 12543,
          bounceRate: 34.2,
          avgSessionDuration: '3m 42s',
          topPages: [
            { page: '/dashboard', views: 8432, change: 12.5 },
            { page: '/users', views: 5621, change: -3.2 },
            { page: '/analytics', views: 3456, change: 8.7 },
            { page: '/settings', views: 2134, change: 15.3 },
          ],
          userGrowth: [
            { month: '1月', users: 1200, change: 8.2 },
            { month: '2月', users: 1350, change: 12.5 },
            { month: '3月', users: 1180, change: -12.6 },
            { month: '4月', users: 1420, change: 20.3 },
            { month: '5月', users: 1680, change: 18.3 },
            { month: '6月', users: 1543, change: -8.2 },
          ]
        };
        
        setData(mockData);
      } catch (error) {
        console.error('Failed to load analytics:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAnalytics();
  }, [timeRange]);

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('zh-CN').format(num);
  };

  const getChangeIcon = (change: number) => {
    return change >= 0 ? (
      <TrendingUp className="h-4 w-4 text-green-500" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-500" />
    );
  };

  const getChangeColor = (change: number) => {
    return change >= 0 ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">数据分析</h2>
          <p className="text-muted-foreground">
            查看系统使用情况和用户行为分析
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="选择时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">最近7天</SelectItem>
              <SelectItem value="30d">最近30天</SelectItem>
              <SelectItem value="90d">最近90天</SelectItem>
              <SelectItem value="1y">最近1年</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">加载分析数据中...</p>
        </div>
      ) : data ? (
        <>
          {/* 概览统计 */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总浏览量</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(data.totalViews)}</div>
                <p className="text-xs text-muted-foreground">
                  +20.1% 较上期
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">独立访客</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(data.uniqueVisitors)}</div>
                <p className="text-xs text-muted-foreground">
                  +15.3% 较上期
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">跳出率</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.bounceRate}%</div>
                <p className="text-xs text-muted-foreground">
                  -2.1% 较上期
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">平均会话时长</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.avgSessionDuration}</div>
                <p className="text-xs text-muted-foreground">
                  +8.2% 较上期
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            {/* 热门页面 */}
            <Card>
              <CardHeader>
                <CardTitle>热门页面</CardTitle>
                <CardDescription>
                  访问量最高的页面
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.topPages.map((page, index) => (
                    <div key={page.page} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted text-sm font-medium">
                          {index + 1}
                        </div>
                        <div>
                          <p className="text-sm font-medium">{page.page}</p>
                          <p className="text-xs text-muted-foreground">
                            {formatNumber(page.views)} 次浏览
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        {getChangeIcon(page.change)}
                        <span className={`text-sm ${getChangeColor(page.change)}`}>
                          {Math.abs(page.change)}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* 用户增长 */}
            <Card>
              <CardHeader>
                <CardTitle>用户增长趋势</CardTitle>
                <CardDescription>
                  每月新增用户数量
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.userGrowth.map((item) => (
                    <div key={item.month} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                          {item.month.charAt(0)}
                        </div>
                        <div>
                          <p className="text-sm font-medium">{item.month}</p>
                          <p className="text-xs text-muted-foreground">
                            {formatNumber(item.users)} 新用户
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        {getChangeIcon(item.change)}
                        <span className={`text-sm ${getChangeColor(item.change)}`}>
                          {Math.abs(item.change)}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      ) : (
        <div className="text-center py-8">
          <p className="text-muted-foreground">暂无数据</p>
        </div>
      )}
    </div>
  );
}

export default withAuth(AnalyticsPage);
