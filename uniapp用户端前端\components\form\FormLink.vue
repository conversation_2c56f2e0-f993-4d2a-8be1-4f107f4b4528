<template>
  <text 
    class="form-link"
    :class="[
      `form-link--${type}`,
      `form-link--${size}`,
      {
        'form-link--disabled': disabled,
        'form-link--underline': underline
      }
    ]"
    @tap="handleClick"
  >
    {{ text }}
  </text>
</template>

<script>
export default {
  name: 'FormLink',
  
  props: {
    // 基础属性
    text: {
      type: String,
      required: true
    },
    type: {
      type: String,
      default: 'primary',
      validator: value => ['primary', 'secondary', 'danger', 'success'].includes(value)
    },
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    disabled: {
      type: Boolean,
      default: false
    },
    underline: {
      type: Boolean,
      default: false
    }
  },
  
  methods: {
    handleClick(e) {
      if (this.disabled) {
        return;
      }
      this.$emit('click', e);
    }
  }
};
</script>

<style scoped>
.form-link {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}

.form-link:active {
  opacity: 0.7;
}

/* 链接类型 */
.form-link--primary {
  color: #F2282D;
}

.form-link--primary:hover {
  color: #d91e22;
}

.form-link--secondary {
  color: #666666;
}

.form-link--secondary:hover {
  color: #333333;
}

.form-link--danger {
  color: #ff4757;
}

.form-link--danger:hover {
  color: #ff3838;
}

.form-link--success {
  color: #2ed573;
}

.form-link--success:hover {
  color: #26c65a;
}

/* 链接尺寸 */
.form-link--small {
  font-size: 12rpx;
  letter-spacing: 0.06rpx;
  line-height: 24rpx;
}

.form-link--medium {
  font-size: 26rpx;
  letter-spacing: 0.13rpx;
  line-height: 51.62rpx;
}

.form-link--large {
  font-size: 30rpx;
  letter-spacing: 0.15rpx;
  line-height: 60rpx;
}

/* 下划线 */
.form-link--underline {
  text-decoration: underline;
}

/* 禁用状态 */
.form-link--disabled {
  color: #ccc !important;
  cursor: not-allowed;
}

.form-link--disabled:active {
  opacity: 1;
}
</style>
