"use client"

import { useState, useEffect, useCallback } from 'react';
import { withAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, RefreshCw } from 'lucide-react';
import { api<PERSON>all, LoadingManager } from '@/lib/api-helpers';

// Import components
import { ActivationCodeStatsCards } from '@/components/activation-codes/activation-code-stats-cards';
import { ActivationCodeFilters } from '@/components/activation-codes/activation-code-filters';
import { ActivationCodeTable } from '@/components/activation-codes/activation-code-table';
import { CreateActivationCodeDialog, ActivationCodeDetailDialog } from '@/components/activation-codes/activation-code-dialogs';

// Import services and types
import { 
  ActivationCodeService, 
  ActivationCodeListParams, 
  ActivationCodeStats,
  ActivationCode 
} from '@/services/activation-code.service';

function ActivationCodesPage() {
  // State management
  const [codes, setCodes] = useState<ActivationCode[]>([]);
  const [stats, setStats] = useState<ActivationCodeStats>({
    total: 0,
    active: 0,
    used: 0,
    expired: 0,
    disabled: 0,
    by_level: { v1: 0, v2: 0, v3: 0, v4: 0 }
  });

  // Loading states are managed by LoadingManager
  const [loadingStates, setLoadingStates] = useState(LoadingManager.getState());

  // Subscribe to loading state changes
  useEffect(() => {
    const unsubscribe = LoadingManager.subscribe(setLoadingStates);
    return unsubscribe;
  }, []);

  // Pagination state
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });

  // Filter state
  const [filters, setFilters] = useState<ActivationCodeListParams>({
    page: 1,
    limit: 20,
    sort_by: 'created_at',
    sort_order: 'desc',
  });

  // Dialog states
  const [createDialog, setCreateDialog] = useState(false);
  const [detailDialog, setDetailDialog] = useState<{
    open: boolean;
    code: ActivationCode | null;
  }>({ open: false, code: null });

  // Load activation codes data
  const loadCodes = useCallback(async () => {
    const result = await apiCall(
      () => ActivationCodeService.getActivationCodes(filters),
      {
        loadingKey: 'loadCodes',
        showErrorToast: true,
      }
    );

    if (result.success && result.data) {
      setCodes(result.data.codes);
      setPagination({
        page: result.data.pagination?.page || 1,
        limit: result.data.pagination?.limit || 20,
        total: result.data.pagination?.total || 0,
        totalPages: result.data.pagination?.totalPages || 0,
      });
    }
  }, [filters]);

  // Load activation code statistics
  const loadStats = useCallback(async () => {
    const result = await apiCall(
      () => ActivationCodeService.getActivationCodeStats(),
      {
        loadingKey: 'loadStats',
        showErrorToast: true,
      }
    );

    if (result.success && result.data) {
      setStats(result.data);
    }
  }, []);

  // Load data on mount and filter changes
  useEffect(() => {
    loadCodes();
  }, [loadCodes]);

  useEffect(() => {
    loadStats();
  }, [loadStats]);

  // Refresh data
  const handleRefresh = () => {
    loadCodes();
    loadStats();
  };

  // Filter handlers
  const handleFiltersChange = (newFilters: ActivationCodeListParams) => {
    setFilters(newFilters);
  };

  const handleFiltersReset = () => {
    setFilters({
      page: 1,
      limit: 20,
      sort_by: 'created_at',
      sort_order: 'desc',
    });
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  // Activation code action handlers
  const handleCreateCodes = async (data: {
    vip_level: string;
    vip_duration_days?: number;
    expires_at?: Date;
    description?: string;
    count?: number;
  }) => {
    const result = await apiCall(
      () => ActivationCodeService.createActivationCodes({
        vip_level: data.vip_level as any,
        vip_duration_days: data.vip_duration_days,
        expires_at: data.expires_at?.toISOString(),
        description: data.description,
        count: data.count,
      }),
      {
        loadingKey: 'createCodes',
        showSuccessToast: true,
        showErrorToast: true,
      }
    );

    if (result.success) {
      setCreateDialog(false);
      loadCodes();
      loadStats();
    }
  };

  const handleViewCode = (code: ActivationCode) => {
    setDetailDialog({ open: true, code });
  };

  const handleEditCode = async (code: ActivationCode) => {
    // TODO: Implement edit functionality
    console.log('Edit code:', code);
  };

  const handleDeleteCode = async (code: ActivationCode) => {
    const result = await apiCall(
      () => ActivationCodeService.deleteActivationCode(code.id),
      {
        loadingKey: 'deleteCode',
        showSuccessToast: true,
        showErrorToast: true,
      }
    );

    if (result.success) {
      loadCodes();
      loadStats();
    }
  };

  const handleStatusChange = async (code: ActivationCode, status: 'active' | 'disabled') => {
    const result = await apiCall(
      () => ActivationCodeService.updateActivationCode(code.id, { status }),
      {
        loadingKey: 'updateStatus',
        showSuccessToast: true,
        showErrorToast: true,
      }
    );

    if (result.success) {
      loadCodes();
      loadStats();
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">激活码管理</h1>
          <p className="text-muted-foreground">
            管理VIP激活码的生成、使用和状态
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            onClick={handleRefresh} 
            disabled={loadingStates.loadCodes || loadingStates.loadStats}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${(loadingStates.loadCodes || loadingStates.loadStats) ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Button onClick={() => setCreateDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            创建激活码
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <ActivationCodeStatsCards stats={stats} loading={loadingStates.loadStats} />

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>激活码列表</CardTitle>
          <CardDescription>
            查看和管理所有激活码
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Filters */}
          <ActivationCodeFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onReset={handleFiltersReset}
          />

          {/* Activation Code Table */}
          <ActivationCodeTable
            codes={codes}
            loading={loadingStates.loadCodes}
            onView={handleViewCode}
            onEdit={handleEditCode}
            onDelete={handleDeleteCode}
            onStatusChange={handleStatusChange}
          />

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                显示 {((pagination.page - 1) * pagination.limit) + 1} 到{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
                共 {pagination.total} 条记录
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1 || loadingStates.loadCodes}
                >
                  上一页
                </Button>
                <span className="text-sm">
                  第 {pagination.page} 页，共 {pagination.totalPages} 页
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages || loadingStates.loadCodes}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      <CreateActivationCodeDialog
        open={createDialog}
        onOpenChange={setCreateDialog}
        onConfirm={handleCreateCodes}
        loading={loadingStates.createCodes}
      />

      <ActivationCodeDetailDialog
        open={detailDialog.open}
        onOpenChange={(open) => setDetailDialog({ open, code: detailDialog.code })}
        code={detailDialog.code}
      />
    </div>
  );
}

export default withAuth(ActivationCodesPage);
