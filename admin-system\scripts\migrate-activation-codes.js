const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'admin_system',
  multipleStatements: true
};

async function migrateActivationCodes() {
  let connection;

  try {
    console.log('🔄 Connecting to MySQL server...');
    connection = await mysql.createConnection(dbConfig);

    console.log('🔄 Creating activation codes tables...');
    
    // Read SQL file
    const sqlFile = path.join(__dirname, 'create-activation-codes-table.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');
    
    // Execute SQL
    await connection.query(sql);
    
    console.log('✅ Activation codes tables created successfully');

    // Check if migration record exists
    const [existingMigration] = await connection.execute(
      'SELECT id FROM migrations WHERE migration_name = ?',
      ['create_activation_codes_tables']
    );

    if (existingMigration.length === 0) {
      // Record migration
      await connection.execute(
        'INSERT INTO migrations (migration_name, executed_at) VALUES (?, NOW())',
        ['create_activation_codes_tables']
      );
      console.log('✅ Migration recorded');
    }

    console.log('🎉 Activation codes migration completed successfully');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateActivationCodes().catch(console.error);
}

module.exports = { migrateActivationCodes };
