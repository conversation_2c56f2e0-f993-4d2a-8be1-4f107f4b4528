"use client"

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Copy, Ticket } from 'lucide-react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { toast } from 'sonner';
import { apiCall } from '@/lib/api-helpers';
import { ActivationCodeService, ActivationCode, VIP_LEVEL_CONFIG } from '@/services/activation-code.service';
import { User } from '@/services/user.service';

interface UserActivationHistoryProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: User | null;
}

export function UserActivationHistory({
  open,
  onOpenChange,
  user
}: UserActivationHistoryProps) {
  const [codes, setCodes] = useState<ActivationCode[]>([]);
  const [loading, setLoading] = useState(false);

  // Load user's activation codes
  useEffect(() => {
    if (open && user) {
      loadUserActivationCodes();
    }
  }, [open, user]);

  const loadUserActivationCodes = async () => {
    if (!user) return;

    setLoading(true);
    const result = await apiCall(
      () => ActivationCodeService.getActivationCodes({
        page: 1,
        limit: 100, // 获取所有记录
        status: 'used', // 只获取已使用的
        sort_by: 'used_at',
        sort_order: 'desc'
      }),
      {
        showErrorToast: true,
      }
    );

    if (result.success && result.data) {
      // 过滤出该用户使用的激活码
      const userCodes = result.data.codes.filter(code => code.used_by === user.id);
      setCodes(userCodes);
    }
    setLoading(false);
  };

  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      toast.success('激活码已复制到剪贴板');
    } catch (error) {
      toast.error('复制失败');
    }
  };

  const getVipLevelBadge = (level: string) => {
    const config = VIP_LEVEL_CONFIG[level as keyof typeof VIP_LEVEL_CONFIG];
    
    if (!config) {
      return <Badge variant="outline">{level}</Badge>;
    }

    return (
      <Badge 
        variant="outline" 
        className="gap-1"
        style={{ 
          borderColor: config.color,
          color: config.color 
        }}
      >
        {config.label}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "PPP", { locale: zhCN });
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Ticket className="h-5 w-5" />
            激活码使用历史
          </DialogTitle>
          <DialogDescription>
            查看用户 <strong>{user.username}</strong> 的激活码使用记录
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {loading ? (
            <div className="space-y-2">
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="h-12 bg-gray-200 rounded animate-pulse"></div>
              ))}
            </div>
          ) : codes.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              该用户暂未使用过激活码
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>激活码</TableHead>
                    <TableHead>VIP等级</TableHead>
                    <TableHead>有效期</TableHead>
                    <TableHead>使用时间</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {codes.map((code) => (
                    <TableRow key={code.id}>
                      <TableCell>
                        <code className="text-sm font-mono bg-muted px-2 py-1 rounded">
                          {ActivationCodeService.formatCode(code.code)}
                        </code>
                      </TableCell>
                      <TableCell>
                        {getVipLevelBadge(code.vip_level)}
                      </TableCell>
                      <TableCell className="text-sm">
                        {code.vip_duration_days} 天
                      </TableCell>
                      <TableCell className="text-sm">
                        {code.used_at ? formatDate(code.used_at) : '-'}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCopyCode(code.code)}
                          className="h-8 w-8 p-0"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* 统计信息 */}
          {codes.length > 0 && (
            <div className="bg-muted/50 rounded-lg p-4">
              <h4 className="font-medium mb-2">使用统计</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="text-muted-foreground">总使用次数</div>
                  <div className="font-medium">{codes.length} 次</div>
                </div>
                <div>
                  <div className="text-muted-foreground">总VIP天数</div>
                  <div className="font-medium">
                    {codes.reduce((sum, code) => sum + code.vip_duration_days, 0)} 天
                  </div>
                </div>
                <div>
                  <div className="text-muted-foreground">最高等级</div>
                  <div className="font-medium">
                    {codes.length > 0 ? 
                      VIP_LEVEL_CONFIG[
                        codes.reduce((highest, code) => 
                          code.vip_level > highest ? code.vip_level : highest, 'v1'
                        ) as keyof typeof VIP_LEVEL_CONFIG
                      ]?.label || 'V1'
                      : '-'
                    }
                  </div>
                </div>
                <div>
                  <div className="text-muted-foreground">首次使用</div>
                  <div className="font-medium">
                    {codes.length > 0 && codes[codes.length - 1]?.used_at ? 
                      formatDate(codes[codes.length - 1].used_at) : '-'
                    }
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
